import axios from 'axios';
import { logger } from './debugLogger';

interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

interface ChatRequest {
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

interface OpenRouterRequest {
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
}

export interface EssayMarkingResult {
  ideasContent: { score: number; comment: string };
  structureOrganization: { score: number; comment: string };
  languageUse: { score: number; comment: string };
  vocabulary: { score: number; comment: string };
  mechanics: { score: number; comment: string };
  creativity: { score: number; comment: string };
  total: number;
  convertedScore: number;
  band: string;
  keyFeedback: string[];
  nextStepAction: string;
}

const systemPrompt = `You are a Bahasa Melayu teacher marking a Year 5 student's essay.
Follow the KSSR 2022 guidelines and the detailed criteria below. You are to mark it very strictly and are not generous with scoring.
For each main criterion, give:

A numerical score (see point ranges).

Specific comments (what worked, what needs improvement).

Finally, total the marks, convert them to the 20-point national scale, state the achievement band (Cemerlang, Kepujian, Baik, Memuaskan, T<PERSON>, TM), and give concise feedback.

1 — Ideas & Content (0 – 25 pts)
Relevance to the task.

Completeness: all parts of the topic addressed.

Logical elaboration with clear examples/supporting details.

2 — Structure & Organization (0 – 20 pts)
Effective introduction and conclusion.

Logical paragraphing; one idea per paragraph.

Smooth flow and coherence; format matches text type (narrative, descriptive, etc.).

Overall presentation neat and coherent.

3 — Language Use (0 – 20 pts)
Grammar (kegramatisan): correct syntax and morphology.

Sentence variety: simple, compound, complex used appropriately.

Function words & adjectives (kata tugas, kata adjektif) used correctly.

Editing & refinement: evidence of self-correction or polished phrasing.

4 — Vocabulary (0 – 15 pts)
Range and suitability of word choice.

Effective, context-appropriate adjectives/verbs.

Reward accurate use of idioms or peribahasa.

5 — Mechanics: Spelling & Punctuation (0 – 10 pts)
Correct spelling of common and topic-specific terms.

Proper use of commas, periods, question/exclamation marks.

Minor errors that do not hinder meaning incur small penalties; frequent or disruptive errors incur larger ones.

6 — Creativity & Originality (0 – 10 pts)
Original ideas or personal insights.

Interesting voice or perspective.

Avoidance of wholesale copying.

Scoring Summary
Band (20-pt scale)	Score Range	Descriptor
Cemerlang (Excellent)	18 – 20	Task fully met; language accurate & varied; presentation exemplary.
Kepujian (Merit)	15 – 17	Minor errors; well-developed ideas; good vocabulary & structure.
Baik (Good)	13 – 14	Some errors but ideas clear; adequate vocabulary; mostly correct format.
Memuaskan (Satisfactory)	8 – 12	Many errors; limited elaboration; inconsistent paragraphing.
TPM – Tahap Penguasaan Minimum	3 – 7	Ideas partly relevant; frequent grammar issues; poor organization.
TM – Tidak Mencapai TPM	0 – 2	Ideas irrelevant/missing; very poor language; no proper format.

Conversion rule: 20-pt score = round(Total / 5)
(e.g., 76/100 → 15/20 → Kepujian)

Output Template
📝 **Essay Marking Report**

1. Ideas & Content: __ /25  
   - Comment:

2. Structure & Organization: __ /20  
   - Comment:

3. Language Use: __ /20  
   - Comment:

4. Vocabulary: __ /15  
   - Comment:

5. Mechanics (Spelling & Punctuation): __ /10  
   - Comment:

6. Creativity & Originality: __ /10  
   - Comment:

**Total**: __ /100  
**Converted (÷5)**: __ /20  
**Band**: Cemerlang / Kepujian / Baik / Memuaskan / TPM / TM

---

### Key Feedback (3 bullets)
- …
- …
- …

**Next-step Action:** One clear, practical suggestion for improvement.

IMPORTANT: You must respond with EXACTLY the format above. Use the exact template structure and fill in the scores and comments appropriately.`;

export const getEssayMarking = async (
  topic: string,
  essayContent: {
    introduction: string;
    mainPoints: string[];
    conclusion: string;
  }
): Promise<EssayMarkingResult | null> => {
  logger.info('Marker', '🚀 Starting essay marking request', { 
    topic,
    essayLength: {
      introduction: essayContent.introduction.length,
      mainPoints: essayContent.mainPoints.map(p => p.length),
      conclusion: essayContent.conclusion.length
    }
  });

  try {
    // Construct the essay text for marking
    const fullEssay = `
**Topic:** ${topic}

**Introduction:**
${essayContent.introduction || '[No introduction provided]'}

**Main Points:**
${essayContent.mainPoints.map((point, index) => 
  `Point ${index + 1}: ${point || '[No content provided]'}`
).join('\n\n')}

**Conclusion:**
${essayContent.conclusion || '[No conclusion provided]'}
    `.trim();

    const prompt = `Please mark this Year 5 Bahasa Melayu essay according to the KSSR 2022 guidelines:

${fullEssay}

Please provide your marking using the exact template format specified in the system prompt.`;

    const messages: OpenRouterRequest['messages'] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt }
    ];

    logger.info('Marker', '📝 Sending essay for marking', {
      essayWordCount: fullEssay.split(' ').length,
      messageCount: messages.length
    });

    // Use a model suitable for detailed analysis and marking
    const model = import.meta.env.VITE_OPENROUTER_MODEL_MARKER || import.meta.env.VITE_OPENROUTER_MODEL_QUESTIONS;

    logger.info('Marker', '🤖 Selected model for marking', { model });

    // Call our local API instead of OpenRouter directly
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
    const response = await axios.post<ChatResponse>(
      `${apiBaseUrl}/api/chat`,
      {
        messages,
        model,
        temperature: 0.2, // Low temperature for consistent, analytical marking
        max_tokens: 1000 // Increased for detailed marking report
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    logger.info('Marker', '✅ Received marking response', {
      status: response.status,
      hasChoices: response.data.choices?.length > 0
    });

    const content = response.data.choices[0].message.content;
    logger.info('Marker', '📤 Processing marking content', {
      contentLength: content.length,
      preview: content.substring(0, 100) + '...'
    });

    // Parse the structured response
    const parsedResult = parseMarkingResponse(content);
    
    if (parsedResult) {
      logger.info('Marker', '✅ Successfully parsed marking result', {
        total: parsedResult.total,
        convertedScore: parsedResult.convertedScore,
        band: parsedResult.band
      });
    } else {
      logger.warn('Marker', '⚠️ Failed to parse marking response, returning raw content');
    }

    return parsedResult;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      logger.error('Marker', '🔴 Network or API error', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
    } else {
      logger.error('Marker', '🔴 Unexpected error', {
        error: error instanceof Error ? error.message : error
      });
    }
    return null;
  }
};

// Helper function to parse the AI response into structured data
function parseMarkingResponse(content: string): EssayMarkingResult | null {
  try {
    // Extract scores using regex patterns
    const ideasMatch = content.match(/1\.\s*Ideas & Content:\s*(\d+)\s*\/25\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
    const structureMatch = content.match(/2\.\s*Structure & Organization:\s*(\d+)\s*\/20\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
    const languageMatch = content.match(/3\.\s*Language Use:\s*(\d+)\s*\/20\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
    const vocabularyMatch = content.match(/4\.\s*Vocabulary:\s*(\d+)\s*\/15\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
    const mechanicsMatch = content.match(/5\.\s*Mechanics.*?:\s*(\d+)\s*\/10\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
    const creativityMatch = content.match(/6\.\s*Creativity & Originality:\s*(\d+)\s*\/10\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);

    const totalMatch = content.match(/\*\*Total\*\*:\s*(\d+)\s*\/100/i);
    const convertedMatch = content.match(/\*\*Converted.*?\*\*:\s*(\d+)\s*\/20/i);
    const bandMatch = content.match(/\*\*Band\*\*:\s*([^\n]+)/i);

    // Extract key feedback bullets
    const feedbackSection = content.match(/### Key Feedback.*?\n((?:- [^\n]+\n?)+)/s);
    const keyFeedback = feedbackSection ?
      feedbackSection[1].split('\n').filter(line => line.trim().startsWith('-')).map(line => line.replace(/^-\s*/, '').trim()) :
      [];

    // Extract next step action
    const nextStepMatch = content.match(/\*\*Next-step Action:\*\*\s*([^\n]+)/i);

    if (!ideasMatch || !structureMatch || !languageMatch || !vocabularyMatch || !mechanicsMatch || !creativityMatch || !totalMatch || !convertedMatch || !bandMatch) {
      logger.warn('Marker', '⚠️ Could not parse all required fields from marking response');
      return null;
    }

    return {
      ideasContent: {
        score: parseInt(ideasMatch[1]),
        comment: ideasMatch[2].trim()
      },
      structureOrganization: {
        score: parseInt(structureMatch[1]),
        comment: structureMatch[2].trim()
      },
      languageUse: {
        score: parseInt(languageMatch[1]),
        comment: languageMatch[2].trim()
      },
      vocabulary: {
        score: parseInt(vocabularyMatch[1]),
        comment: vocabularyMatch[2].trim()
      },
      mechanics: {
        score: parseInt(mechanicsMatch[1]),
        comment: mechanicsMatch[2].trim()
      },
      creativity: {
        score: parseInt(creativityMatch[1]),
        comment: creativityMatch[2].trim()
      },
      total: parseInt(totalMatch[1]),
      convertedScore: parseInt(convertedMatch[1]),
      band: bandMatch[1].trim(),
      keyFeedback: keyFeedback.slice(0, 3), // Ensure max 3 bullets
      nextStepAction: nextStepMatch ? nextStepMatch[1].trim() : 'Continue practicing essay writing.'
    };
  } catch (error) {
    logger.error('Marker', '🔴 Error parsing marking response', { error });
    return null;
  }
}
