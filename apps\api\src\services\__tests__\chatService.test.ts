import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ChatService } from '../chatService.js';
import type { OpenRouterClient, ChatResponse } from '../../clients/openRouter.js';

// Mock OpenRouter client
const mockOpenRouterClient: OpenRouterClient = {
  complete: vi.fn()
};

describe('ChatService', () => {
  let chatService: ChatService;

  beforeEach(() => {
    chatService = new ChatService(mockOpenRouterClient);
    vi.clearAllMocks();
  });

  describe('handleStudentPrompt', () => {
    it('should return success response with valid messages', async () => {
      // Arrange
      const mockResponse: ChatResponse = {
        choices: [{
          message: {
            content: 'Hello! How can I help you today?'
          }
        }]
      };
      
      vi.mocked(mockOpenRouterClient.complete).mockResolvedValue(mockResponse);

      const request = {
        messages: [
          { role: 'user' as const, content: 'Hello' }
        ]
      };

      // Act
      const result = await chatService.handleStudentPrompt(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.error).toBeUndefined();
      expect(mockOpenRouterClient.complete).toHaveBeenCalledWith({
        messages: request.messages,
        model: 'anthropic/claude-3-haiku',
        temperature: 0.7,
        max_tokens: 150
      });
    });

    it('should use custom model and parameters when provided', async () => {
      // Arrange
      const mockResponse: ChatResponse = {
        choices: [{
          message: {
            content: 'Custom response'
          }
        }]
      };
      
      vi.mocked(mockOpenRouterClient.complete).mockResolvedValue(mockResponse);

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }],
        model: 'openai/gpt-4',
        temperature: 0.5,
        max_tokens: 200
      };

      // Act
      const result = await chatService.handleStudentPrompt(request);

      // Assert
      expect(result.success).toBe(true);
      expect(mockOpenRouterClient.complete).toHaveBeenCalledWith({
        messages: request.messages,
        model: 'openai/gpt-4',
        temperature: 0.5,
        max_tokens: 200
      });
    });

    it('should return error for empty messages array', async () => {
      // Arrange
      const request = {
        messages: []
      };

      // Act
      const result = await chatService.handleStudentPrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid request: messages array is required and cannot be empty');
      expect(result.statusCode).toBe(400);
      expect(mockOpenRouterClient.complete).not.toHaveBeenCalled();
    });

    it('should return error for missing messages', async () => {
      // Arrange
      const request = {
        messages: undefined as any
      };

      // Act
      const result = await chatService.handleStudentPrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid request: messages array is required and cannot be empty');
      expect(result.statusCode).toBe(400);
    });

    it('should handle OpenRouter API errors', async () => {
      // Arrange
      const apiError = new Error('OpenRouter API error: 429 Rate limit exceeded');
      vi.mocked(mockOpenRouterClient.complete).mockRejectedValue(apiError);

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }]
      };

      // Act
      const result = await chatService.handleStudentPrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to get response from AI service');
      expect(result.statusCode).toBe(429);
    });

    it('should handle generic errors', async () => {
      // Arrange
      const genericError = new Error('Network error');
      vi.mocked(mockOpenRouterClient.complete).mockRejectedValue(genericError);

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }]
      };

      // Act
      const result = await chatService.handleStudentPrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Internal server error');
      expect(result.statusCode).toBe(500);
    });

    it('should handle OpenRouter API errors without status code', async () => {
      // Arrange
      const apiError = new Error('OpenRouter API error: Invalid request');
      vi.mocked(mockOpenRouterClient.complete).mockRejectedValue(apiError);

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }]
      };

      // Act
      const result = await chatService.handleStudentPrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to get response from AI service');
      expect(result.statusCode).toBe(500);
    });
  });
});
