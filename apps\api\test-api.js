// Simple test script to verify the API is working
// Run with: node test-api.js

const testHealthEndpoint = async () => {
  try {
    const response = await fetch('http://localhost:3001/health');
    const data = await response.json();
    console.log('✅ Health endpoint working:', data);
    return true;
  } catch (error) {
    console.error('❌ Health endpoint failed:', error.message);
    return false;
  }
};

const testChatEndpoint = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'Hello, can you respond with just "API working"?' }
        ],
        model: 'anthropic/claude-3-haiku',
        temperature: 0.1,
        max_tokens: 10
      })
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('❌ Chat endpoint failed:', response.status, errorData);
      return false;
    }

    const data = await response.json();
    console.log('✅ Chat endpoint working:', data.choices[0].message.content);
    return true;
  } catch (error) {
    console.error('❌ Chat endpoint failed:', error.message);
    return false;
  }
};

const runTests = async () => {
  console.log('🧪 Testing API endpoints...\n');
  
  const healthOk = await testHealthEndpoint();
  console.log('');
  
  if (healthOk) {
    await testChatEndpoint();
  } else {
    console.log('⏭️  Skipping chat test due to health check failure');
  }
  
  console.log('\n🏁 Tests completed');
};

runTests();
