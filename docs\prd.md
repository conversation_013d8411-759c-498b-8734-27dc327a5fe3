# Product Requirements Document: BM Essay Tutor

## 1. Overview

BM Essay Tutor is a web application for Year 5 Malaysian students to practice Bahasa Melayu (BM) essay writing. It emulates a test-paper interface, guiding students through Introduction, Main Points, and Conclusion in a single canvas with AI‑powered feedback and bilingual support.

## 2. Objectives

* **Scaffold Essay Structure**: Provide clear sectional guidance (Introduction, Main Points, Conclusion).
* **Non‑disruptive Feedback**: Subtle live underlines and sentence‑level suggestions to minimize interruptions.
* **Bilingual Explanations**: English glosses support limited BM vocabulary.
* **Interactive AI Tutor**: Sidebar for translation and ideation assistance without revealing answers directly.

## 3. User Stories

| As a…          | I want to…                                   | So that…                                      |
| -------------- | -------------------------------------------- | --------------------------------------------- |
| Year 5 student | add/remove main-point paragraphs dynamically | my essay mirrors a real test layout.          |
| Year 5 student | see hints only at sentence boundaries        | I stay focused on writing flow.               |
| Year 5 student | get English explanations of Malay feedback   | I understand corrections despite limited BM.  |
| Year 5 student | ask questions during writing                 | I can clarify translations or generate ideas. |

## 4. Feature Flow

1. **Topic Selection Screen**

   * Title bar with Home, Topic name.
   * Instruction: “Choose a topic.”
   * Topic tiles + “Begin Essay / Mula Menulis.”

2. **Essay Writing Canvas**

   * Single-page layout with three clearly labeled areas:

     * **INTRODUCTION** (fixed)
     * **MAIN POINTS** (addable/removable sections)
     * **CONCLUSION** (fixed)
   * **Add Main Point** button appends numbered text areas.
   * **Subtle Live Underlines** flag errors as you type.
   * **Sentence‑Level Checks** trigger feedback on pause or “.”.
   * **Review & Submit** proceeds to review stage.

3. **AI Tutor Sidebar**

   * Collapsible pane on the right.
   * Dropdown to select **Translation** or **Ideation** mode.
   * Input field for student question + display area for AI response.
   * Auto‑post sentence feedback here when a sentence ends.

4. **Review Screen**

   * Full essay preview with highlighted corrections.
   * Error summary in English (vocabulary, grammar, structure tips).
   * Download/Print/Restart controls.

## 5. Mockup (Low‑Fidelity ASCII)

```text
┌────────────────────────────────────────────────────────────────────────────┐
│ ← Home   Topic: My Family      Progress: ● Intro ● Main ● Conclusion       │
│────────────────────────────────────────────────────────────────────────────│
│ Instruction: "Write your essay in Malay. Include Intro, Main Points, and Conclusion." │
│                                                                            │
│ INTRODUCTION                                                               │
│ ┌────────────────────────────────────────────────────────────────────────┐  │
│ │ Pengenalan: Saya tinggal di kampung kecil...                          │  │
│ └────────────────────────────────────────────────────────────────────────┘  │
│                                                                            │
│ MAIN POINTS                                                                │
│ [ + Add Main Point ]                                                       │
│ 1. ┌────────────────────────────────────────────────────────────────────┐   │
│    │ Saya belajar di sekolah rendah...                                 │   │
│    └────────────────────────────────────────────────────────────────────┘   │
│ 2. (Empty until added)                                                     │
│                                                                            │
│ CONCLUSION                                                                 │
│ ┌────────────────────────────────────────────────────────────────────────┐  │
│ │ Rumusan: Secara ringkas, keluarga ialah...                            │  │
│ └────────────────────────────────────────────────────────────────────────┘  │
│                                                                            │
│ [ Review & Submit ]                                                         │
├────────────────────────────────────────────────────────────────────────────┤
│ █ Essay Canvas █                       █ Ask Tutor Sidebar █               │
│ [Full essay view…]                      [📖 Translation | 💡 Ideation]       │
│                                        │ Student Q: "…"                  │
│                                        │ AI Response: "…"               │
│                                        │ [ Collapse ◀ ]                   │
└────────────────────────────────────────────────────────────────────────────┘
```

## 6. AI Tutor Prompt Templates

### 6.1. General Principles

* **Never directly give answers.** Provide incremental hints to encourage self‑correction.
* **Sentence‑Completion Feedback.** Detect periods (“.”) in the canvas and post one tip in the sidebar.

### 6.2. Translation Activity

```text
You are a Year 5 Malay tutor. The student will ask for an English→Malay translation.
1. Do NOT give the full answer at once. Offer a hint based on initial letters.
2. If guessed incorrectly, provide a more revealing hint but not the full word.
3. If student gives up, reveal the complete answer.

Student: "What is 'give' in Malay?"
AI: "Here’s a hint: it starts with 'Memb'."
Student: "Is it membahagi?"
AI: "Close… here’s another hint: 'Memb***'."
Student: "I give up."
AI: "You’re close: 'Membe**'."
Student: "Memberi?"
AI: "Yes!"
```

### 6.3. Ideation & Sentence-Completion Feedback

```text
You are a Year 5 Malay tutor handling both ideation requests and sentence-completion feedback.

**A. Ideation Requests**
When the student asks for ideas, provide 2–3 concise bullet points in Malay, each followed by an English gloss. Use short phrases (not full paragraphs), then encourage the student to rephrase into full sentences.

Student: "Ideas for 'My Family' main points?"
AI Tutor:
- "Aktiviti hujung minggu bersama" (Weekend activities together)
- "Peranan ibu bapa" (Parents' roles)
- "Momen perayaan keluarga" (Family celebration moments)

AI Tutor: "Try turning these phrases into your own Malay sentences!"

**B. Sentence-Completion Feedback**
After the student completes a sentence in the canvas (detecting a period “.”), post one tip in the sidebar:
1. Identify one grammar or vocabulary improvement, with a brief English note.
2. Optionally offer a structural tip (e.g., vary conjunctions).

[Student ends a sentence]
AI Tutor Sidebar: "Nice work! Consider using 'serta' instead of 'dan' to vary your linking words."  
```

## 7. Next Steps

* Define detailed acceptance criteria.
* Draft component hierarchy and API specs.
* Create high‑fidelity mockups and style guide.
* Iterate based on teacher/student feedback.

*End of PRD*
