import React, { useState, useEffect, useRef } from 'react';
import Header from './Header';
import { ArrowLeftIcon, PrinterIcon, DownloadIcon, LoaderIcon } from 'lucide-react';
import { getEssayMarking, EssayMarkingResult } from '../utils/markerClient';
const ReviewScreen = ({
  topic,
  essayContent,
  onBackToEssay,
  onBackToHome
}) => {
  const [markingResult, setMarkingResult] = useState<EssayMarkingResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const markEssay = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const result = await getEssayMarking(topic, essayContent);
        setMarkingResult(result);
      } catch (err) {
        setError('Failed to mark essay. Please try again.');
        console.error('Essay marking error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    markEssay();
  }, [topic, essayContent]);

  const handleDownloadPDF = async () => {
    if (!contentRef.current) return;
    const html2pdf = (await import('html2pdf.js')).default;
    html2pdf()
      .from(contentRef.current)
      .set({
        margin: 0.5,
        filename: `${topic || 'essay'}-review.pdf`,
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
      })
      .save();
  };

  return <div className="w-full min-h-screen flex flex-col">
      <Header title="Review Essay" topic={topic} onBackToHome={onBackToHome} />
      <div className="flex-grow p-4 pb-16 bg-gray-50 overflow-y-auto">
        <div className="max-w-4xl mx-auto" ref={contentRef}>
          <div className="flex justify-between items-center mb-6">
            <button onClick={onBackToEssay} className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeftIcon size={18} className="mr-1" />
              Back to Editor
            </button>
            <div className="flex space-x-4">
              <button className="flex items-center text-gray-600 hover:text-gray-800" onClick={() => window.print()}>
                <PrinterIcon size={18} className="mr-1" />
                Print
              </button>
              <button className="flex items-center text-gray-600 hover:text-gray-800" onClick={handleDownloadPDF}>
                <DownloadIcon size={18} className="mr-1" />
                Download
              </button>
              <button onClick={onBackToHome} className="flex items-center text-gray-600 hover:text-gray-800">
                <div size={18} className="mr-1" />
                Start New Essay
              </button>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-8 mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">
              {topic}
            </h2>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Introduction</h3>
              <p className="whitespace-pre-wrap">
                {essayContent.introduction || 'No introduction written.'}
              </p>
            </div>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Main Points</h3>
              {essayContent.mainPoints.map((point, index) => <div key={index} className="mb-4">
                  <h4 className="font-medium">Point {index + 1}</h4>
                  <p className="whitespace-pre-wrap">
                    {point || 'No content for this point.'}
                  </p>
                </div>)}
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Conclusion</h3>
              <p className="whitespace-pre-wrap">
                {essayContent.conclusion || 'No conclusion written.'}
              </p>
            </div>
          </div>
          {/* AI Marking Results */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4">📝 Essay Marking Report</h3>

            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <LoaderIcon className="animate-spin mr-2" size={20} />
                <span>Marking your essay...</span>
              </div>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <p className="text-red-700">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-2 text-red-600 hover:text-red-800 underline"
                >
                  Try Again
                </button>
              </div>
            )}

            {markingResult && (
              <div className="space-y-6">
                {/* Score Summary */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-semibold text-lg">Overall Score</h4>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">
                        {markingResult.convertedScore}/20
                      </div>
                      <div className="text-sm text-gray-600">
                        ({markingResult.total}/100)
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                      markingResult.band === 'Cemerlang' ? 'bg-green-100 text-green-800' :
                      markingResult.band === 'Kepujian' ? 'bg-blue-100 text-blue-800' :
                      markingResult.band === 'Baik' ? 'bg-yellow-100 text-yellow-800' :
                      markingResult.band === 'Memuaskan' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {markingResult.band}
                    </span>
                  </div>
                </div>

                {/* Detailed Scores */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4">
                    <h5 className="font-medium text-blue-700 mb-2">Ideas & Content</h5>
                    <div className="text-lg font-semibold mb-1">{markingResult.ideasContent.score}/25</div>
                    <p className="text-sm text-gray-600">{markingResult.ideasContent.comment}</p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h5 className="font-medium text-green-700 mb-2">Structure & Organization</h5>
                    <div className="text-lg font-semibold mb-1">{markingResult.structureOrganization.score}/20</div>
                    <p className="text-sm text-gray-600">{markingResult.structureOrganization.comment}</p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h5 className="font-medium text-purple-700 mb-2">Language Use</h5>
                    <div className="text-lg font-semibold mb-1">{markingResult.languageUse.score}/20</div>
                    <p className="text-sm text-gray-600">{markingResult.languageUse.comment}</p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h5 className="font-medium text-indigo-700 mb-2">Vocabulary</h5>
                    <div className="text-lg font-semibold mb-1">{markingResult.vocabulary.score}/15</div>
                    <p className="text-sm text-gray-600">{markingResult.vocabulary.comment}</p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h5 className="font-medium text-red-700 mb-2">Mechanics</h5>
                    <div className="text-lg font-semibold mb-1">{markingResult.mechanics.score}/10</div>
                    <p className="text-sm text-gray-600">{markingResult.mechanics.comment}</p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h5 className="font-medium text-pink-700 mb-2">Creativity & Originality</h5>
                    <div className="text-lg font-semibold mb-1">{markingResult.creativity.score}/10</div>
                    <p className="text-sm text-gray-600">{markingResult.creativity.comment}</p>
                  </div>
                </div>

                {/* Key Feedback */}
                {markingResult.keyFeedback.length > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h5 className="font-medium text-yellow-800 mb-3">Key Feedback</h5>
                    <ul className="list-disc pl-5 space-y-1">
                      {markingResult.keyFeedback.map((feedback, index) => (
                        <li key={index} className="text-yellow-700">{feedback}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Next Step Action */}
                {markingResult.nextStepAction && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h5 className="font-medium text-green-800 mb-2">Next Step Action</h5>
                    <p className="text-green-700">{markingResult.nextStepAction}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>;
};
export default ReviewScreen;