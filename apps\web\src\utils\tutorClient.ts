import axios from 'axios';
import { logger } from './debugLogger';

interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

interface ChatRequest {
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

const systemPrompt = `You are a Year 5 Malay tutor handling both ideation requests and sentence-completion feedback.

**Guidelines for Ideation Requests**
When the student asks for ideas:
1. Provide 2–3 concise bullet points in Malay
2. Include English gloss for each point in parentheses
3. Use short phrases, not full paragraphs
4. End with an encouragement to convert ideas into full sentences
5. NEVER write complete essays or paragraphs

Example Ideation Response:
- "Aktiviti hujung minggu bersama" (Weekend activities together)
- "Peranan ibu bapa" (Parents' roles)
- "Momen perayaan keluarga" (Family celebration moments)

Try turning these phrases into your own Malay sentences!

**Guidelines for Sentence Feedback**
When providing feedback:
1. Focus on one improvement at a time
2. Offer grammar or vocabulary suggestions with English explanations
3. Optionally suggest structural improvements (e.g., varying conjunctions)
4. Keep feedback friendly and encouraging
5. NEVER rewrite the entire sentence
6. Limit feedback to one tip per sentence and no more than three tips per session.
7. Stop offering advice once the student applies a suggestion; do not repeat hints for the same mistake.
ALWAYS USE ENGLISH TO EXPLAIN AND KEEP THE FEEDBACK SHORT (1-2 sentences)`;


export const getTutorResponse = async (
  prompt: string,
  previousMessages: { role: 'user' | 'assistant'; content: string }[] = [],
  isSentenceFeedback: boolean = false
): Promise<string> => {
  logger.info('Tutor', '🚀 Starting new tutor request', { 
    prompt,
    type: isSentenceFeedback ? 'sentence-feedback' : 'ideation'
  });
  
  if (!prompt) {
    logger.warn('Tutor', '⚠️ Empty prompt received');
    return '';
  }

  try {
    // Construct conversation history
    const messages: ChatRequest['messages'] = [
      { role: 'system', content: systemPrompt },
      ...previousMessages,
      { role: 'user', content: prompt }
    ];

    logger.info('Tutor', '📝 Using conversation history', {
      messageCount: messages.length,
      lastUserMessage: prompt,
      fullHistory: messages
    });

    // Select model based on activity type
    const model = isSentenceFeedback
      ? import.meta.env.VITE_OPENROUTER_MODEL_QUESTIONS
      : import.meta.env.VITE_OPENROUTER_MODEL_IDEATION;

    logger.info('Tutor', '🤖 Selected model for request', {
      model,
      activityType: isSentenceFeedback ? 'feedback' : 'ideation'
    });

    // Call our local API instead of OpenRouter directly
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
    const response = await axios.post<ChatResponse>(
      `${apiBaseUrl}/api/chat`,
      {
        messages,
        model,
        temperature: isSentenceFeedback ? 0.3 : 0.7, // Lower temperature for feedback, higher for creative ideation
        max_tokens: 200
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    logger.info('Tutor', '✅ Received API response', {
      status: response.status,
      hasChoices: response.data.choices?.length > 0,
      headers: response.headers
    });

    const content = response.data.choices[0].message.content;
    logger.info('Tutor', '📤 Processing response content', {
      contentLength: content.length,
      preview: content.substring(0, 50) + '...',
      fullResponse: response.data
    });

    return content;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      logger.error('Tutor', '🔴 Network or API error', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });
    } else {
      logger.error('Tutor', '🔴 Unexpected error', {
        error: error instanceof Error ? error.message : error
      });
    }
    return 'Sorry, I encountered an error. Please try again later.';
  }
};
