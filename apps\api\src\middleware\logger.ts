import pino from 'pino';
import pinoHttp from 'pino-http';

// Create logger instance with configuration based on environment
export const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  transport: process.env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname'
    }
  } : undefined,
  // In production, use structured JSON logging
  formatters: {
    level: (label) => {
      return { level: label };
    }
  },
  // Remove sensitive information from logs
  redact: {
    paths: ['req.headers.authorization', 'req.headers.cookie', 'req.body.apiKey'],
    censor: '[REDACTED]'
  }
});

// HTTP request logger middleware
export const httpLogger = pinoHttp({
  logger,
  // Custom request ID generation
  genReqId: (req) => {
    return req.headers['x-request-id'] || 
           req.headers['x-correlation-id'] || 
           Math.random().toString(36).substring(2, 15);
  },
  // Custom serializers for request/response
  serializers: {
    req: (req) => ({
      method: req.method,
      url: req.url,
      headers: {
        'user-agent': req.headers['user-agent'],
        'content-type': req.headers['content-type'],
        'x-forwarded-for': req.headers['x-forwarded-for']
      },
      remoteAddress: req.remoteAddress,
      remotePort: req.remotePort
    }),
    res: (res) => ({
      statusCode: res.statusCode,
      headers: {
        'content-type': typeof res.getHeader === 'function' ? res.getHeader('content-type') : res.headers?.['content-type'],
        'content-length': typeof res.getHeader === 'function' ? res.getHeader('content-length') : res.headers?.['content-length']
      }
    })
  },
  // Custom log level based on status code
  customLogLevel: (req, res, err) => {
    if (res.statusCode >= 400 && res.statusCode < 500) {
      return 'warn';
    } else if (res.statusCode >= 500 || err) {
      return 'error';
    } else if (res.statusCode >= 300 && res.statusCode < 400) {
      return 'info';
    }
    return 'info';
  }
});
