import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import nock from 'nock';
import { OpenRouterClientImpl } from '../openRouter.js';

// Mock fetch globally for these tests
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('OpenRouterClientImpl', () => {
  let client: OpenRouterClientImpl;
  const apiKey = 'test-api-key';

  beforeEach(() => {
    client = new OpenRouterClientImpl(apiKey);
    vi.clearAllMocks();
  });

  describe('complete', () => {
    it('should make successful API call with default parameters', async () => {
      // Arrange
      const mockResponse = {
        choices: [{
          message: {
            content: 'Hello! How can I help you?'
          }
        }]
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const request = {
        messages: [
          { role: 'user' as const, content: 'Hello' }
        ]
      };

      // Act
      const result = await client.complete(request);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:3000',
            'X-Title': 'BM Essay Tutor'
          }),
          body: JSON.stringify({
            messages: [{ role: 'user', content: 'Hello' }],
            model: 'anthropic/claude-3-haiku',
            temperature: 0.7,
            max_tokens: 150
          })
        })
      );
    });

    it('should use custom parameters when provided', async () => {
      // Arrange
      const mockResponse = {
        choices: [{
          message: {
            content: 'Custom response'
          }
        }]
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }],
        model: 'openai/gpt-4',
        temperature: 0.5,
        max_tokens: 200
      };

      // Act
      const result = await client.complete(request);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          body: JSON.stringify({
            messages: [{ role: 'user', content: 'Test' }],
            model: 'openai/gpt-4',
            temperature: 0.5,
            max_tokens: 200
          })
        })
      );
    });

    it('should use default values when parameters not provided', async () => {
      // Arrange
      const mockResponse = {
        choices: [{
          message: {
            content: 'Default response'
          }
        }]
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }]
      };

      // Act
      const result = await client.complete(request);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          body: JSON.stringify({
            messages: [{ role: 'user', content: 'Test' }],
            model: 'anthropic/claude-3-haiku',
            temperature: 0.7,
            max_tokens: 150
          })
        })
      );
    });

    it('should throw error for non-200 response', async () => {
      // Arrange
      const errorResponse = 'Rate limit exceeded';

      mockFetch.mockResolvedValue({
        ok: false,
        status: 429,
        text: () => Promise.resolve(errorResponse)
      });

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }]
      };

      // Act & Assert
      await expect(client.complete(request)).rejects.toThrow('OpenRouter API error: 429');
    });

    it('should throw error for network failure', async () => {
      // Arrange
      mockFetch.mockRejectedValue(new Error('Network error'));

      const request = {
        messages: [{ role: 'user' as const, content: 'Test' }]
      };

      // Act & Assert
      await expect(client.complete(request)).rejects.toThrow('Network error');
    });
  });
});
