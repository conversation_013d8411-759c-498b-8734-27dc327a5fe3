import axios from 'axios';

interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

interface ChatRequest {
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

const systemPrompt = `You are a Year 5 Malay tutor assisting with English to Malay translations.
Follow these rules strictly:
1. Do NOT give the full answer at once. Offer a hint based on initial letters.
2. If guessed incorrectly, provide a more revealing hint but not the full word.
3. If student gives up, reveal the complete answer.
4. Your responses should be helpful but encourage learning through hints.
5. Keep explanations simple and clear for Year 5 students.`;

export const getOpenRouterResponse = async (
  prompt: string,
  previousMessages: { role: 'user' | 'assistant'; content: string }[] = []
): Promise<string> => {
  if (!prompt) return '';

  try {
    // Construct conversation history
    const messages: ChatRequest['messages'] = [
      { role: 'system', content: systemPrompt },
      ...previousMessages,
      { role: 'user', content: prompt }
    ];

    // Select model based on activity type
    const getModelForActivity = (prompt: string) => {
      if (prompt.toLowerCase().includes('translate') || prompt.match(/["'].*["'] in malay/i)) {
        return import.meta.env.VITE_OPENROUTER_MODEL_TRANSLATION;
      } else if (prompt.toLowerCase().includes('idea') || prompt.toLowerCase().includes('suggest')) {
        return import.meta.env.VITE_OPENROUTER_MODEL_IDEATION;
      } else {
        return import.meta.env.VITE_OPENROUTER_MODEL_QUESTIONS;
      }
    };

    // Call our local API instead of OpenRouter directly
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
    const response = await axios.post<ChatResponse>(
      `${apiBaseUrl}/api/chat`,
      {
        messages,
        model: getModelForActivity(prompt),
        temperature: 0.7,
        max_tokens: 150
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling chat API:', error);
    return 'Sorry, I encountered an error. Please try again later.';
  }
};
