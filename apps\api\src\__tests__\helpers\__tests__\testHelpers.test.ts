import { describe, it, expect } from 'vitest';
import {
  createMockOpenRouterClient,
  createMockChatResponse,
  createOpenRouterError,
  createChatRequest,
  TEST_MESSAGES,
  TEST_CONFIG
} from '../testHelpers.js';

describe('Test Helpers', () => {
  describe('createMockOpenRouterClient', () => {
    it('should create a mock client with complete method', () => {
      const client = createMockOpenRouterClient();
      
      expect(client).toHaveProperty('complete');
      expect(typeof client.complete).toBe('function');
    });
  });

  describe('createMockChatResponse', () => {
    it('should create a response with default content', () => {
      const response = createMockChatResponse();
      
      expect(response.choices).toHaveLength(1);
      expect(response.choices[0].message.content).toBe('Test response');
    });

    it('should create a response with custom content', () => {
      const customContent = 'Custom test response';
      const response = createMockChatResponse(customContent);
      
      expect(response.choices[0].message.content).toBe(customContent);
    });
  });

  describe('createOpenRouterError', () => {
    it('should create an error with status code and message', () => {
      const error = createOpenRouterError(429, 'Rate limit exceeded');
      
      expect(error.message).toBe('OpenRouter API error: 429 Rate limit exceeded');
    });

    it('should create an error with default message', () => {
      const error = createOpenRouterError(500);
      
      expect(error.message).toBe('OpenRouter API error: 500 API Error');
    });
  });

  describe('TEST_MESSAGES', () => {
    it('should provide standard test messages', () => {
      expect(TEST_MESSAGES.simple).toHaveLength(1);
      expect(TEST_MESSAGES.simple[0].role).toBe('user');
      expect(TEST_MESSAGES.simple[0].content).toBe('Hello');

      expect(TEST_MESSAGES.essay).toHaveLength(2);
      expect(TEST_MESSAGES.essay[0].role).toBe('system');
      expect(TEST_MESSAGES.essay[1].role).toBe('user');

      expect(TEST_MESSAGES.empty).toHaveLength(0);
    });
  });

  describe('TEST_CONFIG', () => {
    it('should provide standard configuration values', () => {
      expect(TEST_CONFIG.defaultModel).toBe('anthropic/claude-3-haiku');
      expect(TEST_CONFIG.defaultTemperature).toBe(0.7);
      expect(TEST_CONFIG.defaultMaxTokens).toBe(150);
    });
  });

  describe('createChatRequest', () => {
    it('should create a request with default values', () => {
      const request = createChatRequest();
      
      expect(request.messages).toEqual(TEST_MESSAGES.simple);
      expect(request.model).toBe(TEST_CONFIG.defaultModel);
      expect(request.temperature).toBe(TEST_CONFIG.defaultTemperature);
      expect(request.max_tokens).toBe(TEST_CONFIG.defaultMaxTokens);
    });

    it('should create a request with overrides', () => {
      const overrides = {
        messages: TEST_MESSAGES.essay,
        temperature: 0.5
      };
      const request = createChatRequest(overrides);
      
      expect(request.messages).toEqual(TEST_MESSAGES.essay);
      expect(request.temperature).toBe(0.5);
      expect(request.model).toBe(TEST_CONFIG.defaultModel); // Should keep default
    });
  });
});
