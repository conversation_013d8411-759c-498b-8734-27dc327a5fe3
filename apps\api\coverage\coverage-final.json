{"D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\.eslintrc.cjs": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\.eslintrc.cjs", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 18}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 13}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 12}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 25}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 4}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 34}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 56}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 38}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 18}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 26}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 25}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 4}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 10}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 80}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 4}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 466}, "end": {"line": 18, "column": 2}}, "locations": [{"start": {"line": 1, "column": 466}, "end": {"line": 18, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 466}, "end": {"line": 18, "column": 2}}, "loc": {"start": {"line": 1, "column": 466}, "end": {"line": 18, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\index.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\index.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 28}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 63}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 56}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 59}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 60}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 86}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 86}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 0}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 29}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 16}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 0}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 56}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 24}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 0}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 65}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 28}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 17}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 50}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 18}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 76}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 79}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 5}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 62}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 48}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 3}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 0}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 41}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 51}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 47}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 0}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 50}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 40}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 43}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 3}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 0}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 40}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 0}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 46}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 16}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 49}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 58}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 80}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 22}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 29}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 54}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 59}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 6}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 0}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 34}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 56}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 43}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 0}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 53}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 36}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 42}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 14}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 19}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 42}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 56}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 7}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 5}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 0}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 40}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 66}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 97}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 0}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 9}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 81}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 0}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 44}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 18}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 38}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 34}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 24}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 9}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 0}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 56}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 17}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 14}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 20}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 18}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 9}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 0}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 42}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 48}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 20}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 60}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 11}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 30}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 14}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 44}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 20}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 30}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 39}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 11}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 51}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 48}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 11}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 7}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 0}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 21}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 57}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 18}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 72}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 55}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 77}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 9}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 0}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 55}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 28}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 39}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 55}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 75}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 10}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 9}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 5}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 5}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 0}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 13}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 1}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 0}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 44}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 26}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 38}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 0}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 24}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 67}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 66}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 69}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 3}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 12, "15": 12, "16": 12, "17": 12, "18": 12, "19": 2, "20": 2, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 11, "28": 11, "29": 11, "30": 11, "31": 11, "32": 11, "33": 12, "34": 0, "35": 0, "36": 11, "37": 11, "38": 11, "39": 11, "40": 11, "41": 11, "42": 0, "43": 11, "44": 12, "45": 12, "46": 12, "47": 12, "48": 12, "49": 12, "50": 12, "51": 12, "52": 12, "53": 12, "54": 12, "55": 12, "56": 2, "57": 2, "58": 2, "59": 2, "60": 2, "61": 2, "62": 12, "63": 12, "64": 12, "65": 12, "66": 5, "67": 5, "68": 5, "69": 5, "70": 5, "71": 5, "72": 5, "73": 5, "74": 5, "75": 5, "76": 5, "77": 5, "78": 5, "79": 5, "80": 5, "81": 5, "82": 5, "83": 5, "84": 5, "85": 5, "86": 2, "87": 2, "88": 2, "89": 2, "90": 2, "91": 5, "92": 3, "93": 3, "94": 3, "95": 3, "96": 3, "97": 3, "98": 3, "99": 3, "100": 3, "101": 5, "102": 5, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 12, "119": 12, "120": 12, "121": 12, "122": 1, "123": 1, "124": 1, "125": 1, "126": 1, "127": 1, "128": 1, "129": 1, "130": 1, "131": 1}, "branchMap": {"0": {"type": "branch", "line": 126, "loc": {"start": {"line": 126, "column": 25}, "end": {"line": 126, "column": 38}}, "locations": [{"start": {"line": 126, "column": 25}, "end": {"line": 126, "column": 38}}]}, "1": {"type": "branch", "line": 14, "loc": {"start": {"line": 14, "column": 7}, "end": {"line": 122, "column": 1}}, "locations": [{"start": {"line": 14, "column": 7}, "end": {"line": 122, "column": 1}}]}, "2": {"type": "branch", "line": 19, "loc": {"start": {"line": 19, "column": 16}, "end": {"line": 27, "column": 3}}, "locations": [{"start": {"line": 19, "column": 16}, "end": {"line": 27, "column": 3}}]}, "3": {"type": "branch", "line": 21, "loc": {"start": {"line": 21, "column": 17}, "end": {"line": 27, "column": 3}}, "locations": [{"start": {"line": 21, "column": 17}, "end": {"line": 27, "column": 3}}]}, "4": {"type": "branch", "line": 27, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 34, "column": 39}}, "locations": [{"start": {"line": 27, "column": 2}, "end": {"line": 34, "column": 39}}]}, "5": {"type": "branch", "line": 34, "loc": {"start": {"line": 34, "column": 39}, "end": {"line": 36, "column": 3}}, "locations": [{"start": {"line": 34, "column": 39}, "end": {"line": 36, "column": 3}}]}, "6": {"type": "branch", "line": 36, "loc": {"start": {"line": 36, "column": 2}, "end": {"line": 42, "column": 49}}, "locations": [{"start": {"line": 36, "column": 2}, "end": {"line": 42, "column": 49}}]}, "7": {"type": "branch", "line": 42, "loc": {"start": {"line": 42, "column": 37}, "end": {"line": 43, "column": 58}}, "locations": [{"start": {"line": 42, "column": 37}, "end": {"line": 43, "column": 58}}]}, "8": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 36}, "end": {"line": 44, "column": 80}}, "locations": [{"start": {"line": 43, "column": 36}, "end": {"line": 44, "column": 80}}]}, "9": {"type": "branch", "line": 56, "loc": {"start": {"line": 56, "column": 21}, "end": {"line": 63, "column": 3}}, "locations": [{"start": {"line": 56, "column": 21}, "end": {"line": 63, "column": 3}}]}, "10": {"type": "branch", "line": 61, "loc": {"start": {"line": 61, "column": 31}, "end": {"line": 61, "column": 56}}, "locations": [{"start": {"line": 61, "column": 31}, "end": {"line": 61, "column": 56}}]}, "11": {"type": "branch", "line": 66, "loc": {"start": {"line": 66, "column": 45}, "end": {"line": 119, "column": 3}}, "locations": [{"start": {"line": 66, "column": 45}, "end": {"line": 119, "column": 3}}]}, "12": {"type": "branch", "line": 75, "loc": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 34}}, "locations": [{"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 34}}]}, "13": {"type": "branch", "line": 86, "loc": {"start": {"line": 86, "column": 17}, "end": {"line": 86, "column": 41}}, "locations": [{"start": {"line": 86, "column": 17}, "end": {"line": 86, "column": 41}}]}, "14": {"type": "branch", "line": 86, "loc": {"start": {"line": 86, "column": 41}, "end": {"line": 92, "column": 13}}, "locations": [{"start": {"line": 86, "column": 41}, "end": {"line": 92, "column": 13}}]}, "15": {"type": "branch", "line": 92, "loc": {"start": {"line": 92, "column": 6}, "end": {"line": 101, "column": 7}}, "locations": [{"start": {"line": 92, "column": 6}, "end": {"line": 101, "column": 7}}]}, "16": {"type": "branch", "line": 98, "loc": {"start": {"line": 98, "column": 26}, "end": {"line": 98, "column": 43}}, "locations": [{"start": {"line": 98, "column": 26}, "end": {"line": 98, "column": 43}}]}, "17": {"type": "branch", "line": 99, "loc": {"start": {"line": 99, "column": 24}, "end": {"line": 99, "column": 48}}, "locations": [{"start": {"line": 99, "column": 24}, "end": {"line": 99, "column": 48}}]}, "18": {"type": "branch", "line": 103, "loc": {"start": {"line": 103, "column": 4}, "end": {"line": 118, "column": 5}}, "locations": [{"start": {"line": 103, "column": 4}, "end": {"line": 118, "column": 5}}]}, "19": {"type": "branch", "line": 128, "loc": {"start": {"line": 128, "column": 17}, "end": {"line": 132, "column": 3}}, "locations": [{"start": {"line": 128, "column": 17}, "end": {"line": 132, "column": 3}}]}}, "b": {"0": [0], "1": [12], "2": [2], "3": [1], "4": [11], "5": [0], "6": [11], "7": [0], "8": [11], "9": [2], "10": [0], "11": [5], "12": [4], "13": [2], "14": [2], "15": [3], "16": [0], "17": [0], "18": [0], "19": [1]}, "fnMap": {"0": {"name": "buildServer", "decl": {"start": {"line": 14, "column": 7}, "end": {"line": 122, "column": 1}}, "loc": {"start": {"line": 14, "column": 7}, "end": {"line": 122, "column": 1}}, "line": 14}}, "f": {"0": 12}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\__tests__\\helpers\\testHelpers.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\__tests__\\helpers\\testHelpers.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 28}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 82}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 3}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 47}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 3}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 64}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 10}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 21}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 4}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 1}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 58}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 3}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 81}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 10}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 15}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 16}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 15}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 7}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 6}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 4}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 0}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 3}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 50}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 3}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 89}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 69}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 1}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 0}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 3}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 48}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 3}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 30}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 56}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 10}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 81}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 91}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 4}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 11}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 2}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 0}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 3}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 31}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 3}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 28}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 43}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 26}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 23}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 2}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 0}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 3}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 55}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 3}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 56}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 10}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 35}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 36}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 48}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 45}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 16}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 4}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 2, "17": 2, "18": 2, "19": 2, "20": 2, "21": 2, "22": 2, "23": 2, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 2, "30": 2, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1, "43": 1, "44": 1, "45": 1, "46": 1, "47": 1, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 1, "55": 1, "56": 1, "57": 2, "58": 2, "59": 2, "60": 2, "61": 2, "62": 2, "63": 2, "64": 2}, "branchMap": {"0": {"type": "branch", "line": 7, "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 11, "column": 1}}, "locations": [{"start": {"line": 7, "column": 7}, "end": {"line": 11, "column": 1}}]}, "1": {"type": "branch", "line": 16, "loc": {"start": {"line": 16, "column": 7}, "end": {"line": 24, "column": 1}}, "locations": [{"start": {"line": 16, "column": 7}, "end": {"line": 24, "column": 1}}]}, "2": {"type": "branch", "line": 29, "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 31, "column": 1}}, "locations": [{"start": {"line": 29, "column": 7}, "end": {"line": 31, "column": 1}}]}, "3": {"type": "branch", "line": 57, "loc": {"start": {"line": 57, "column": 7}, "end": {"line": 65, "column": 1}}, "locations": [{"start": {"line": 57, "column": 7}, "end": {"line": 65, "column": 1}}]}}, "b": {"0": [1], "1": [2], "2": [2], "3": [2]}, "fnMap": {"0": {"name": "createMockOpenRouterClient", "decl": {"start": {"line": 7, "column": 7}, "end": {"line": 11, "column": 1}}, "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 11, "column": 1}}, "line": 7}, "1": {"name": "createMockChatResponse", "decl": {"start": {"line": 16, "column": 7}, "end": {"line": 24, "column": 1}}, "loc": {"start": {"line": 16, "column": 7}, "end": {"line": 24, "column": 1}}, "line": 16}, "2": {"name": "createOpenRouterError", "decl": {"start": {"line": 29, "column": 7}, "end": {"line": 31, "column": 1}}, "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 31, "column": 1}}, "line": 29}, "3": {"name": "createChatRequest", "decl": {"start": {"line": 57, "column": 7}, "end": {"line": 65, "column": 1}}, "loc": {"start": {"line": 57, "column": 7}, "end": {"line": 65, "column": 1}}, "line": 57}}, "f": {"0": 1, "1": 2, "2": 2, "3": 2}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\clients\\openRouter.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\clients\\openRouter.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 18}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 1}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 30}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 26}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 17}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 23}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 22}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 1}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 31}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 12}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 14}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 22}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 6}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 6}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 1}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 0}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 35}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 56}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 1}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 0}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 63}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 40}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 0}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 63}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 106}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 0}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 83}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 21}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 16}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 49}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 43}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 48}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 35}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 8}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 28}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 17}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 14}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 20}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 18}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 8}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 0}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 23}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 46}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 79}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 5}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 0}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 33}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 3}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 5, "29": 5, "30": 5, "31": 5, "32": 5, "33": 5, "34": 5, "35": 5, "36": 5, "37": 5, "38": 5, "39": 5, "40": 5, "41": 5, "42": 5, "43": 5, "44": 5, "45": 4, "46": 5, "47": 1, "48": 1, "49": 1, "50": 3, "51": 3, "52": 5, "53": 1}, "branchMap": {"0": {"type": "branch", "line": 26, "loc": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}, "locations": [{"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}]}, "1": {"type": "branch", "line": 28, "loc": {"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}, "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}]}, "2": {"type": "branch", "line": 45, "loc": {"start": {"line": 45, "column": 5}, "end": {"line": 47, "column": 22}}, "locations": [{"start": {"line": 45, "column": 5}, "end": {"line": 47, "column": 22}}]}, "3": {"type": "branch", "line": 47, "loc": {"start": {"line": 47, "column": 22}, "end": {"line": 50, "column": 5}}, "locations": [{"start": {"line": 47, "column": 22}, "end": {"line": 50, "column": 5}}]}, "4": {"type": "branch", "line": 50, "loc": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 33}}, "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 33}}]}}, "b": {"0": [6], "1": [5], "2": [4], "3": [1], "4": [3]}, "fnMap": {"0": {"name": "OpenRouterClientImpl", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}, "loc": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}, "line": 26}, "1": {"name": "complete", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}, "loc": {"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}, "line": 28}}, "f": {"0": 6, "1": 5}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\middleware\\logger.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\middleware\\logger.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 24}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 33}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 65}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 28}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 41}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 55}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 26}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 14}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 21}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 36}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 28}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 5}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 16}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 47}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 15}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 23}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 30}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 5}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 4}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 43}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 11}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 82}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 24}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 3}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 3}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 0}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 33}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 36}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 9}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 33}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 22}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 42}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 46}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 55}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 4}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 44}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 16}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 20}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 25}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 19}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 16}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 48}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 52}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 57}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 8}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 39}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 32}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 7}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 20}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 33}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 16}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 124}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 129}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 7}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 6}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 4}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 42}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 38}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 56}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 20}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 46}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 21}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 63}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 20}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 5}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 18}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 3}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 3}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 1, "14": 1, "15": 1, "16": 1, "17": 6, "18": 6, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 0, "33": 0, "34": 0, "35": 0, "36": 1, "37": 1, "38": 1, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 1, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 1, "57": 1, "58": 1, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 1}, "branchMap": {"0": {"type": "branch", "line": 7, "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 14, "column": 6}}, "locations": [{"start": {"line": 7, "column": 38}, "end": {"line": 14, "column": 6}}]}, "1": {"type": "branch", "line": 17, "loc": {"start": {"line": 17, "column": 11}, "end": {"line": 19, "column": 5}}, "locations": [{"start": {"line": 17, "column": 11}, "end": {"line": 19, "column": 5}}]}}, "b": {"0": [0], "1": [6]}, "fnMap": {"0": {"name": "level", "decl": {"start": {"line": 17, "column": 11}, "end": {"line": 19, "column": 5}}, "loc": {"start": {"line": 17, "column": 11}, "end": {"line": 19, "column": 5}}, "line": 17}, "1": {"name": "genReqId", "decl": {"start": {"line": 32, "column": 12}, "end": {"line": 36, "column": 4}}, "loc": {"start": {"line": 32, "column": 12}, "end": {"line": 36, "column": 4}}, "line": 32}, "2": {"name": "req", "decl": {"start": {"line": 39, "column": 9}, "end": {"line": 49, "column": 7}}, "loc": {"start": {"line": 39, "column": 9}, "end": {"line": 49, "column": 7}}, "line": 39}, "3": {"name": "res", "decl": {"start": {"line": 50, "column": 9}, "end": {"line": 56, "column": 6}}, "loc": {"start": {"line": 50, "column": 9}, "end": {"line": 56, "column": 6}}, "line": 50}, "4": {"name": "customLogLevel", "decl": {"start": {"line": 59, "column": 18}, "end": {"line": 68, "column": 3}}, "loc": {"start": {"line": 59, "column": 18}, "end": {"line": 68, "column": 3}}, "line": 59}}, "f": {"0": 6, "1": 0, "2": 0, "3": 0, "4": 0}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\middleware\\security.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\middleware\\security.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 43}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 28}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 37}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 30}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 38}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 34}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 43}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 12}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 31}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 60}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 28}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 4}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 79}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 62}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 52}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 35}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 69}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 53}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 53}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 44}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 5}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 18}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 4}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 43}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 45}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 28}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 56}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 4}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 40}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 15}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 19}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 25}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 25}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 42}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 7}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 4}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 26}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 33}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 62}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 30}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 7}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 4}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 41}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 27}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 34}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 3}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 3}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 0}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 44}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 39}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 28}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 26}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 17}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 29}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 46}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 28}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 44}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 54}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 26}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 28}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 27}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 27}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 6}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 4}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 33}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 68}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 33}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 56}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 25}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 39}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 18}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 33}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 20}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 22}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 43}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 9}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 31}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 28}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 17}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 4}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 15}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 17}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 13}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 16}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 25}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 27}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 36}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 38}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 20}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 44}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 21}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 17}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 3}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 0}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 48}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 77}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 24}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 46}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 18}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 3}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 2}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 31}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 33}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 66}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 59}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 2}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 18}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 50}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 23}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 25}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 25}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 16}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 62}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 57}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 7}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 7}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 4}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 33}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 30}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 80}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 7}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 3}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 2}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 9}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 7, "19": 7, "20": 7, "21": 0, "22": 0, "23": 7, "24": 7, "25": 1, "26": 1, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 1, "45": 1, "46": 9, "47": 9, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 1, "68": 1, "69": 1, "70": 1, "71": 1, "72": 1, "73": 1, "74": 1, "75": 1, "76": 1, "77": 1, "78": 1, "79": 1, "80": 1, "81": 1, "82": 1, "83": 1, "84": 1, "85": 1, "86": 1, "87": 1, "88": 1, "89": 1, "90": 1, "91": 1, "92": 1, "93": 1, "94": 1, "95": 1, "96": 1, "97": 1, "98": 9, "99": 9, "100": 9, "101": 9, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 9, "108": 9, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0}, "branchMap": {"0": {"type": "branch", "line": 18, "loc": {"start": {"line": 18, "column": 16}, "end": {"line": 25, "column": 4}}, "locations": [{"start": {"line": 18, "column": 16}, "end": {"line": 25, "column": 4}}]}, "1": {"type": "branch", "line": 21, "loc": {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 52}}, "locations": [{"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 52}}]}, "2": {"type": "branch", "line": 21, "loc": {"start": {"line": 21, "column": 52}, "end": {"line": 23, "column": 5}}, "locations": [{"start": {"line": 21, "column": 52}, "end": {"line": 23, "column": 5}}]}, "3": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 8}, "end": {"line": 48, "column": 3}}, "locations": [{"start": {"line": 46, "column": 8}, "end": {"line": 48, "column": 3}}]}, "4": {"type": "branch", "line": 98, "loc": {"start": {"line": 98, "column": 7}, "end": {"line": 127, "column": 1}}, "locations": [{"start": {"line": 98, "column": 7}, "end": {"line": 127, "column": 1}}]}, "5": {"type": "branch", "line": 102, "loc": {"start": {"line": 102, "column": 2}, "end": {"line": 106, "column": 66}}, "locations": [{"start": {"line": 102, "column": 2}, "end": {"line": 106, "column": 66}}]}, "6": {"type": "branch", "line": 106, "loc": {"start": {"line": 106, "column": 56}, "end": {"line": 107, "column": 59}}, "locations": [{"start": {"line": 106, "column": 56}, "end": {"line": 107, "column": 59}}]}, "7": {"type": "branch", "line": 109, "loc": {"start": {"line": 109, "column": 17}, "end": {"line": 127, "column": 1}}, "locations": [{"start": {"line": 109, "column": 17}, "end": {"line": 127, "column": 1}}]}}, "b": {"0": [7], "1": [0], "2": [0], "3": [9], "4": [9], "5": [0], "6": [0], "7": [0]}, "fnMap": {"0": {"name": "keyGenerator", "decl": {"start": {"line": 18, "column": 16}, "end": {"line": 25, "column": 4}}, "loc": {"start": {"line": 18, "column": 16}, "end": {"line": 25, "column": 4}}, "line": 18}, "1": {"name": "handler", "decl": {"start": {"line": 27, "column": 11}, "end": {"line": 44, "column": 4}}, "loc": {"start": {"line": 27, "column": 11}, "end": {"line": 44, "column": 4}}, "line": 27}, "2": {"name": "skip", "decl": {"start": {"line": 46, "column": 8}, "end": {"line": 48, "column": 3}}, "loc": {"start": {"line": 46, "column": 8}, "end": {"line": 48, "column": 3}}, "line": 46}, "3": {"name": "enforceHTTPS", "decl": {"start": {"line": 98, "column": 7}, "end": {"line": 127, "column": 1}}, "loc": {"start": {"line": 98, "column": 7}, "end": {"line": 127, "column": 1}}, "line": 98}}, "f": {"0": 7, "1": 0, "2": 9, "3": 9}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\middleware\\validation.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\middleware\\validation.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 24}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 63}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 31}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 36}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 48}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 21}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 46}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 61}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 3}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 30}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 36}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 38}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 47}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 52}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 31}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 51}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 52}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 3}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 0}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 34}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 69}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 0}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 32}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 60}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 63}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 9}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 30}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 51}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 6}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 41}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 31}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 6}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 34}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 53}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 27}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 27}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 61}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 9}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 6}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 13}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 21}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 40}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 31}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 50}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 29}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 29}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 31}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 26}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 11}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 8}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 48}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 37}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 40}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 45}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 38}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 32}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 13}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 11}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 7}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 6}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 40}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 51}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 27}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 27}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 72}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 24}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 9}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 6}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 35}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 38}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 9}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 5}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 4}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 1}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 0}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 57}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 70}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 0}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 38}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 87}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 63}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 73}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 4}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 39}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 50}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 27}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 27}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 22}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 21}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 24}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 9}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 6}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 35}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 43}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 40}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 9}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 5}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 4}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 11}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 4}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 7, "29": 7, "30": 7, "31": 7, "32": 7, "33": 7, "34": 7, "35": 7, "36": 7, "37": 7, "38": 7, "39": 7, "40": 7, "41": 7, "42": 7, "43": 7, "44": 2, "45": 2, "46": 2, "47": 2, "48": 2, "49": 2, "50": 2, "51": 2, "52": 2, "53": 2, "54": 2, "55": 2, "56": 2, "57": 2, "58": 2, "59": 2, "60": 2, "61": 2, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 2, "68": 2, "69": 2, "70": 2, "71": 2, "72": 2, "73": 2, "74": 2, "75": 7, "76": 1, "77": 1, "78": 1, "79": 1, "80": 1, "81": 1, "82": 1, "83": 11, "84": 9, "85": 9, "86": 9, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 9, "101": 9, "102": 9, "103": 11}, "branchMap": {"0": {"type": "branch", "line": 27, "loc": {"start": {"line": 27, "column": 7}, "end": {"line": 77, "column": 1}}, "locations": [{"start": {"line": 27, "column": 7}, "end": {"line": 77, "column": 1}}]}, "1": {"type": "branch", "line": 28, "loc": {"start": {"line": 28, "column": 9}, "end": {"line": 76, "column": 4}}, "locations": [{"start": {"line": 28, "column": 9}, "end": {"line": 76, "column": 4}}]}, "2": {"type": "branch", "line": 44, "loc": {"start": {"line": 44, "column": 4}, "end": {"line": 75, "column": 5}}, "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 75, "column": 5}}]}, "3": {"type": "branch", "line": 62, "loc": {"start": {"line": 62, "column": 6}, "end": {"line": 68, "column": 56}}, "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 68, "column": 56}}]}, "4": {"type": "branch", "line": 68, "loc": {"start": {"line": 68, "column": 46}, "end": {"line": 68, "column": 72}}, "locations": [{"start": {"line": 68, "column": 46}, "end": {"line": 68, "column": 72}}]}, "5": {"type": "branch", "line": 57, "loc": {"start": {"line": 57, "column": 36}, "end": {"line": 60, "column": 12}}, "locations": [{"start": {"line": 57, "column": 36}, "end": {"line": 60, "column": 12}}]}, "6": {"type": "branch", "line": 83, "loc": {"start": {"line": 83, "column": 7}, "end": {"line": 104, "column": 1}}, "locations": [{"start": {"line": 83, "column": 7}, "end": {"line": 104, "column": 1}}]}, "7": {"type": "branch", "line": 84, "loc": {"start": {"line": 84, "column": 9}, "end": {"line": 103, "column": 4}}, "locations": [{"start": {"line": 84, "column": 9}, "end": {"line": 103, "column": 4}}]}, "8": {"type": "branch", "line": 85, "loc": {"start": {"line": 85, "column": 63}, "end": {"line": 85, "column": 71}}, "locations": [{"start": {"line": 85, "column": 63}, "end": {"line": 85, "column": 71}}]}, "9": {"type": "branch", "line": 87, "loc": {"start": {"line": 87, "column": 38}, "end": {"line": 100, "column": 5}}, "locations": [{"start": {"line": 87, "column": 38}, "end": {"line": 100, "column": 5}}]}}, "b": {"0": [1], "1": [7], "2": [2], "3": [0], "4": [0], "5": [2], "6": [11], "7": [9], "8": [2], "9": [0]}, "fnMap": {"0": {"name": "validateRequest", "decl": {"start": {"line": 27, "column": 7}, "end": {"line": 77, "column": 1}}, "loc": {"start": {"line": 27, "column": 7}, "end": {"line": 77, "column": 1}}, "line": 27}, "1": {"name": "validateRequestSize", "decl": {"start": {"line": 83, "column": 7}, "end": {"line": 104, "column": 1}}, "loc": {"start": {"line": 83, "column": 7}, "end": {"line": 104, "column": 1}}, "line": 83}}, "f": {"0": 1, "1": 11}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\services\\chatService.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\services\\chatService.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 87}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 36}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 17}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 23}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 22}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 1}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 0}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 38}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 19}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 22}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 17}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 22}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 1}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 0}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 26}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 60}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 0}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 88}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 9}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 25}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 99}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 16}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 25}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 83}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 25}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 10}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 7}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 0}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 38}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 72}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 0}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 31}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 61}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 35}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 29}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 48}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 45}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 9}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 0}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 14}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 22}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 22}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 8}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 0}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 21}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 72}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 6}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 50}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 85}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 79}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 72}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 8}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 16}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 25}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 58}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 20}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 10}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 7}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 0}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 14}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 23}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 39}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 23}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 8}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 5}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 3}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 12, "21": 12, "22": 12, "23": 2, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 10, "30": 10, "31": 12, "32": 12, "33": 12, "34": 12, "35": 12, "36": 12, "37": 12, "38": 12, "39": 12, "40": 4, "41": 4, "42": 4, "43": 4, "44": 4, "45": 4, "46": 12, "47": 6, "48": 6, "49": 6, "50": 6, "51": 3, "52": 3, "53": 3, "54": 3, "55": 3, "56": 3, "57": 3, "58": 3, "59": 3, "60": 3, "61": 3, "62": 3, "63": 3, "64": 3, "65": 3, "66": 3, "67": 12, "68": 1}, "branchMap": {"0": {"type": "branch", "line": 18, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}, "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}]}, "1": {"type": "branch", "line": 20, "loc": {"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}, "locations": [{"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}]}, "2": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 67}}, "locations": [{"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 67}}]}, "3": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 62}, "end": {"line": 23, "column": 98}}, "locations": [{"start": {"line": 23, "column": 62}, "end": {"line": 23, "column": 98}}]}, "4": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 98}, "end": {"line": 29, "column": 7}}, "locations": [{"start": {"line": 23, "column": 98}, "end": {"line": 29, "column": 7}}]}, "5": {"type": "branch", "line": 29, "loc": {"start": {"line": 29, "column": 6}, "end": {"line": 32, "column": 45}}, "locations": [{"start": {"line": 29, "column": 6}, "end": {"line": 32, "column": 45}}]}, "6": {"type": "branch", "line": 32, "loc": {"start": {"line": 32, "column": 36}, "end": {"line": 32, "column": 72}}, "locations": [{"start": {"line": 32, "column": 36}, "end": {"line": 32, "column": 72}}]}, "7": {"type": "branch", "line": 38, "loc": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 48}}, "locations": [{"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 48}}]}, "8": {"type": "branch", "line": 39, "loc": {"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 45}}, "locations": [{"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 45}}]}, "9": {"type": "branch", "line": 40, "loc": {"start": {"line": 40, "column": 7}, "end": {"line": 47, "column": 13}}, "locations": [{"start": {"line": 40, "column": 7}, "end": {"line": 47, "column": 13}}]}, "10": {"type": "branch", "line": 47, "loc": {"start": {"line": 47, "column": 4}, "end": {"line": 67, "column": 5}}, "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 67, "column": 5}}]}, "11": {"type": "branch", "line": 51, "loc": {"start": {"line": 51, "column": 84}, "end": {"line": 67, "column": 5}}, "locations": [{"start": {"line": 51, "column": 84}, "end": {"line": 67, "column": 5}}]}, "12": {"type": "branch", "line": 53, "loc": {"start": {"line": 53, "column": 27}, "end": {"line": 53, "column": 68}}, "locations": [{"start": {"line": 53, "column": 27}, "end": {"line": 53, "column": 68}}]}, "13": {"type": "branch", "line": 53, "loc": {"start": {"line": 53, "column": 64}, "end": {"line": 53, "column": 72}}, "locations": [{"start": {"line": 53, "column": 64}, "end": {"line": 53, "column": 72}}]}}, "b": {"0": [18], "1": [12], "2": [11], "3": [11], "4": [2], "5": [10], "6": [8], "7": [8], "8": [8], "9": [4], "10": [6], "11": [3], "12": [2], "13": [1]}, "fnMap": {"0": {"name": "ChatService", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}, "line": 18}, "1": {"name": "handleStudentPrompt", "decl": {"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}, "loc": {"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}, "line": 20}}, "f": {"0": 18, "1": 12}}}