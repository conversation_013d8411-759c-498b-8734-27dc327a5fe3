{"D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\.eslintrc.cjs": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\.eslintrc.cjs", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 18}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 13}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 12}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 25}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 37}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 4}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 44}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 38}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 18}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 26}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 25}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 4}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 10}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 80}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 4}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 412}, "end": {"line": 17, "column": 2}}, "locations": [{"start": {"line": 1, "column": 412}, "end": {"line": 17, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 412}, "end": {"line": 17, "column": 2}}, "loc": {"start": {"line": 1, "column": 412}, "end": {"line": 17, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\index.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 28}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 63}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 56}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 59}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 29}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 16}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 0}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 56}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 24}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 0}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 65}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 28}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 17}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 50}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 18}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 79}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 5}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 62}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 48}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 3}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 0}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 15}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 16}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 49}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 74}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 80}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 21}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 6}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 26}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 0}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 26}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 36}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 68}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 5}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 0}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 24}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 45}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 9}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 81}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 0}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 56}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 17}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 14}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 20}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 18}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 9}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 0}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 42}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 30}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 14}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 51}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 48}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 11}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 7}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 0}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 21}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 50}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 28}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 39}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 73}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 9}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 5}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 5}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 0}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 13}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 1}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 0}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 44}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 26}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 38}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 0}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 24}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 67}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 66}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 69}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 3}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -271}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -271}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -271}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -271}}, "line": 1}}, "f": {"0": 0}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\__tests__\\helpers\\testHelpers.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\__tests__\\helpers\\testHelpers.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 28}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 82}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 3}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 47}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 3}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 64}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 10}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 21}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 4}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 1}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 58}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 3}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 89}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 10}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 15}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 16}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 15}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 7}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 6}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 4}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 0}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 3}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 50}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 3}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 97}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 69}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 1}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 0}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 3}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 48}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 3}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 30}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 56}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 10}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 81}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 91}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 4}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 11}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 2}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 0}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 3}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 31}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 3}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 28}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 43}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 26}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 23}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 2}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 0}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 3}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 55}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 3}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 56}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 10}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 35}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 36}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 48}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 45}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 16}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 4}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 65, "column": -242}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 65, "column": -242}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 65, "column": -242}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 65, "column": -242}}, "line": 1}}, "f": {"0": 0}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\clients\\openRouter.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\clients\\openRouter.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 18}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 1}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 30}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 26}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 17}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 23}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 22}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 1}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 31}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 12}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 14}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 22}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 6}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 6}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 1}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 0}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 35}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 56}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 1}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 0}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 63}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 40}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 0}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 63}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 106}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 0}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 83}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 21}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 16}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 49}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 43}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 48}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 35}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 8}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 28}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 17}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 14}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 20}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 18}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 8}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 0}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 23}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 46}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 79}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 5}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 0}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 33}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 3}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 5, "29": 5, "30": 5, "31": 5, "32": 5, "33": 5, "34": 5, "35": 5, "36": 5, "37": 5, "38": 5, "39": 5, "40": 5, "41": 5, "42": 5, "43": 5, "44": 5, "45": 4, "46": 5, "47": 1, "48": 1, "49": 1, "50": 3, "51": 3, "52": 5, "53": 1}, "branchMap": {"0": {"type": "branch", "line": 26, "loc": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}, "locations": [{"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}]}, "1": {"type": "branch", "line": 28, "loc": {"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}, "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}]}, "2": {"type": "branch", "line": 45, "loc": {"start": {"line": 45, "column": 5}, "end": {"line": 47, "column": 22}}, "locations": [{"start": {"line": 45, "column": 5}, "end": {"line": 47, "column": 22}}]}, "3": {"type": "branch", "line": 47, "loc": {"start": {"line": 47, "column": 22}, "end": {"line": 50, "column": 5}}, "locations": [{"start": {"line": 47, "column": 22}, "end": {"line": 50, "column": 5}}]}, "4": {"type": "branch", "line": 50, "loc": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 33}}, "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 33}}]}}, "b": {"0": [5], "1": [5], "2": [4], "3": [1], "4": [3]}, "fnMap": {"0": {"name": "OpenRouterClientImpl", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}, "loc": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 40}}, "line": 26}, "1": {"name": "complete", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}, "loc": {"start": {"line": 28, "column": 2}, "end": {"line": 53, "column": 3}}, "line": 28}}, "f": {"0": 5, "1": 5}}, "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\services\\chatService.ts": {"path": "D:\\projects\\GitHub\\bm-essay-tutor\\apps\\api\\src\\services\\chatService.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 87}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 36}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 17}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 23}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 22}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 1}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 0}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 38}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 19}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 22}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 17}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 22}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 1}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 0}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 26}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 60}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 0}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 88}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 9}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 25}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 99}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 16}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 25}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 83}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 25}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 10}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 7}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 0}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 38}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 72}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 0}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 31}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 61}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 35}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 29}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 48}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 45}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 9}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 0}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 14}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 22}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 22}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 8}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 0}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 21}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 72}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 6}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 50}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 85}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 79}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 72}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 8}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 16}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 25}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 58}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 20}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 10}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 7}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 0}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 14}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 23}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 39}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 23}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 8}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 5}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 3}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 7, "21": 7, "22": 7, "23": 2, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 5, "30": 5, "31": 7, "32": 7, "33": 7, "34": 7, "35": 7, "36": 7, "37": 7, "38": 7, "39": 7, "40": 2, "41": 2, "42": 2, "43": 2, "44": 2, "45": 2, "46": 7, "47": 3, "48": 3, "49": 3, "50": 3, "51": 2, "52": 2, "53": 2, "54": 2, "55": 2, "56": 2, "57": 2, "58": 2, "59": 2, "60": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 7, "68": 1}, "branchMap": {"0": {"type": "branch", "line": 18, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}, "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}]}, "1": {"type": "branch", "line": 20, "loc": {"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}, "locations": [{"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}]}, "2": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 67}}, "locations": [{"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 67}}]}, "3": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 62}, "end": {"line": 23, "column": 98}}, "locations": [{"start": {"line": 23, "column": 62}, "end": {"line": 23, "column": 98}}]}, "4": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 98}, "end": {"line": 29, "column": 7}}, "locations": [{"start": {"line": 23, "column": 98}, "end": {"line": 29, "column": 7}}]}, "5": {"type": "branch", "line": 29, "loc": {"start": {"line": 29, "column": 6}, "end": {"line": 32, "column": 45}}, "locations": [{"start": {"line": 29, "column": 6}, "end": {"line": 32, "column": 45}}]}, "6": {"type": "branch", "line": 32, "loc": {"start": {"line": 32, "column": 36}, "end": {"line": 32, "column": 72}}, "locations": [{"start": {"line": 32, "column": 36}, "end": {"line": 32, "column": 72}}]}, "7": {"type": "branch", "line": 38, "loc": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 48}}, "locations": [{"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 48}}]}, "8": {"type": "branch", "line": 39, "loc": {"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 45}}, "locations": [{"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 45}}]}, "9": {"type": "branch", "line": 40, "loc": {"start": {"line": 40, "column": 7}, "end": {"line": 47, "column": 13}}, "locations": [{"start": {"line": 40, "column": 7}, "end": {"line": 47, "column": 13}}]}, "10": {"type": "branch", "line": 47, "loc": {"start": {"line": 47, "column": 4}, "end": {"line": 67, "column": 5}}, "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 67, "column": 5}}]}, "11": {"type": "branch", "line": 51, "loc": {"start": {"line": 51, "column": 84}, "end": {"line": 60, "column": 7}}, "locations": [{"start": {"line": 51, "column": 84}, "end": {"line": 60, "column": 7}}]}, "12": {"type": "branch", "line": 53, "loc": {"start": {"line": 53, "column": 27}, "end": {"line": 53, "column": 68}}, "locations": [{"start": {"line": 53, "column": 27}, "end": {"line": 53, "column": 68}}]}, "13": {"type": "branch", "line": 53, "loc": {"start": {"line": 53, "column": 64}, "end": {"line": 53, "column": 72}}, "locations": [{"start": {"line": 53, "column": 64}, "end": {"line": 53, "column": 72}}]}, "14": {"type": "branch", "line": 60, "loc": {"start": {"line": 60, "column": 6}, "end": {"line": 67, "column": 5}}, "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 67, "column": 5}}]}}, "b": {"0": [7], "1": [7], "2": [6], "3": [6], "4": [2], "5": [5], "6": [4], "7": [4], "8": [4], "9": [2], "10": [3], "11": [2], "12": [1], "13": [1], "14": [1]}, "fnMap": {"0": {"name": "ChatService", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 60}}, "line": 18}, "1": {"name": "handleStudentPrompt", "decl": {"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}, "loc": {"start": {"line": 20, "column": 2}, "end": {"line": 68, "column": 3}}, "line": 20}}, "f": {"0": 7, "1": 7}}}