import { z } from 'zod';
import type { Request, Response, NextFunction } from 'express';
import { logger } from './logger.js';

// Zod schema for chat messages
const ChatMessageSchema = z.object({
  role: z.enum(['system', 'user', 'assistant']),
  content: z.string()
    .min(1, 'Message content cannot be empty')
    .max(200, 'Message content cannot exceed 200 characters')
});

// Zod schema for chat request
const ChatRequestSchema = z.object({
  messages: z.array(ChatMessageSchema)
    .min(1, 'At least one message is required')
    .max(4, 'Cannot exceed 4 messages per request'),
  model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  max_tokens: z.number().min(1).max(1000).optional()
});

// Type for validated chat request
export type ValidatedChatRequest = z.infer<typeof ChatRequestSchema>;

// Validation middleware factory
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate request body
      const validatedData = schema.parse(req.body);
      
      // Attach validated data to request
      req.body = validatedData;
      
      // Log successful validation
      logger.debug('Request validation successful', {
        endpoint: req.path,
        method: req.method,
        validatedFields: Object.keys(validatedData as object)
      });
      
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Log validation error
        logger.warn('Request validation failed', {
          endpoint: req.path,
          method: req.method,
          errors: error.errors,
          clientIP: req.ip
        });
        
        // Return user-friendly validation error
        return res.status(400).json({
          error: 'Invalid request data',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      
      // Log unexpected validation error
      logger.error('Unexpected validation error', {
        endpoint: req.path,
        method: req.method,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP: req.ip
      });
      
      return res.status(500).json({
        error: 'Internal server error'
      });
    }
  };
}

// Pre-configured validation middleware for chat requests
export const validateChatRequest = validateRequest(ChatRequestSchema);

// Middleware to validate request size
export function validateRequestSize(maxSizeBytes: number = 10 * 1024) { // 10KB default
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    
    if (contentLength > maxSizeBytes) {
      logger.warn('Request size exceeded limit', {
        endpoint: req.path,
        method: req.method,
        contentLength,
        maxSizeBytes,
        clientIP: req.ip
      });
      
      return res.status(413).json({
        error: 'Request payload too large',
        maxSize: `${maxSizeBytes} bytes`
      });
    }
    
    next();
  };
}
