{"name": "bm-essay-tutor", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "pnpm --filter web dev", "dev:api": "pnpm --filter api dev", "dev:all": "concurrently \"pnpm dev:api\" \"pnpm dev\"", "build": "pnpm --filter web build", "build:api": "pnpm --filter api build", "lint": "pnpm --filter web lint && pnpm --filter api lint", "preview": "pnpm --filter web preview"}, "devDependencies": {"concurrently": "^8.2.2"}}