import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { OpenRouterClientImpl } from './clients/openRouter.js';
import { ChatService } from './services/chatService.js';
import type { ChatRequest } from './clients/openRouter.js';
import { logger, httpLogger } from './middleware/logger.js';
import { rateLimiter, securityHeaders, enforceHTTPS } from './middleware/security.js';
import { validateChatRequest, validateRequestSize } from './middleware/validation.js';

// Load environment variables
dotenv.config();

export function buildServer(chatService?: ChatService) {
  const app = express();

  // Initialize chat service if not provided (for production use)
  let service = chatService;
  if (!service) {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      logger.error('OPENROUTER_API_KEY not found in environment variables');
      throw new Error('OPENROUTER_API_KEY not found in environment variables');
    }
    const openRouterClient = new OpenRouterClientImpl(apiKey);
    service = new ChatService(openRouterClient);
  }

  // Security middleware (order matters!)
  app.use(enforceHTTPS); // HTTPS enforcement first
  app.use(securityHeaders); // Security headers

  // Only use HTTP logger in non-test environments
  if (process.env.NODE_ENV !== 'test') {
    app.use(httpLogger); // Request logging
  }

  app.use(rateLimiter); // Rate limiting

  // CORS configuration with strict allow-list
  app.use(cors({
    origin: process.env.NODE_ENV === 'production'
      ? ['https://bm-essay-tutor.my'] // Production domain
      : ['http://localhost:5173', 'http://localhost:3000'], // Development ports
    credentials: true,
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    optionsSuccessStatus: 200 // For legacy browser support
  }));

  // Body parsing with size limits
  app.use(validateRequestSize(10 * 1024)); // 10KB limit
  app.use(express.json({ limit: '10kb' }));

  // Health check endpoint (no rate limiting applied)
  app.get('/health', (req, res) => {
    logger.info('Health check requested');
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  // Chat proxy endpoint with validation
  app.post('/api/chat', validateChatRequest, async (req, res) => {
    const requestId = req.headers['x-request-id'] || Math.random().toString(36).substring(2, 15);

    try {
      const { messages, model, temperature, max_tokens }: ChatRequest = req.body;

      logger.info('Chat request received', {
        requestId,
        messageCount: messages.length,
        model: model || 'default',
        clientIP: req.ip
      });

      const result = await service.handleStudentPrompt({
        messages,
        model,
        temperature,
        max_tokens
      });

      if (result.success && result.data) {
        logger.info('Chat request successful', {
          requestId,
          responseLength: JSON.stringify(result.data).length
        });
        res.json(result.data);
      } else {
        logger.warn('Chat request failed', {
          requestId,
          error: result.error,
          statusCode: result.statusCode
        });
        res.status(result.statusCode || 500).json({
          error: result.error || 'Unknown error'
        });
      }

    } catch (error) {
      logger.error('Unexpected error in chat endpoint', {
        requestId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: process.env.NODE_ENV === 'development' ?
               (error instanceof Error ? error.stack : undefined) : undefined
      });

      // Never leak sensitive information in production
      res.status(500).json({
        error: 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      });
    }
  });

  return app;
}

// Start server if this file is run directly
const app = buildServer();
const port = process.env.PORT || 3001;

app.listen(port, () => {
  console.log(`🚀 API server running on http://localhost:${port}`);
  console.log(`📋 Health check: http://localhost:${port}/health`);
  console.log(`💬 Chat endpoint: http://localhost:${port}/api/chat`);
});
