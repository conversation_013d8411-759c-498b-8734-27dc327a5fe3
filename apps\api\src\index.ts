import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { OpenRouterClientImpl } from './clients/openRouter.js';
import { ChatService } from './services/chatService.js';
import type { ChatRequest } from './clients/openRouter.js';

// Load environment variables
dotenv.config();

export function buildServer(chatService?: ChatService) {
  const app = express();

  // Initialize chat service if not provided (for production use)
  let service = chatService;
  if (!service) {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      throw new Error('OPENROUTER_API_KEY not found in environment variables');
    }
    const openRouterClient = new OpenRouterClientImpl(apiKey);
    service = new ChatService(openRouterClient);
  }

  // Middleware
  app.use(cors({
    origin: process.env.NODE_ENV === 'production'
      ? ['https://your-domain.com'] // Replace with your production domain
      : ['http://localhost:5173', 'http://localhost:3000'], // Vite default port
    credentials: true
  }));
  app.use(express.json());

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  // Chat proxy endpoint
  app.post('/api/chat', async (req, res) => {
    try {
      const { messages, model, temperature, max_tokens }: ChatRequest = req.body;

      const result = await service.handleStudentPrompt({
        messages,
        model,
        temperature,
        max_tokens
      });

      if (result.success && result.data) {
        res.json(result.data);
      } else {
        res.status(result.statusCode || 500).json({
          error: result.error || 'Unknown error'
        });
      }

    } catch (error) {
      console.error('Error in /api/chat:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  return app;
}

// Start server if this file is run directly
const app = buildServer();
const port = process.env.PORT || 3001;

app.listen(port, () => {
  console.log(`🚀 API server running on http://localhost:${port}`);
  console.log(`📋 Health check: http://localhost:${port}/health`);
  console.log(`💬 Chat endpoint: http://localhost:${port}/api/chat`);
});
