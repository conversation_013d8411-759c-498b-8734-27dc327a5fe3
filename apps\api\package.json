{"name": "api", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "test": "vitest run --coverage", "test:watch": "vitest", "test:unit": "vitest run src/services src/clients", "test:integration": "vitest run src/__tests__", "test:ci": "vitest run --coverage --reporter=verbose", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext .js,.ts"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "pino": "^9.7.0", "pino-http": "^10.5.0", "zod": "^3.25.57"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-rate-limit": "^6.0.2", "@types/node": "^20.11.18", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitest/coverage-v8": "^1", "eslint": "^8.50.0", "nock": "^13", "pino-pretty": "^13.0.0", "supertest": "^7", "tsx": "^4.7.0", "typescript": "^5.5.4", "vitest": "^1"}}