name: API Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/api/**'
      - '.github/workflows/api-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/api/**'
      - '.github/workflows/api-tests.yml'

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Run API tests
      run: pnpm --filter api test
      env:
        CI: true

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      if: matrix.node-version == '20.x'
      with:
        file: ./apps/api/coverage/lcov.info
        flags: api
        name: api-coverage
        fail_ci_if_error: false

    - name: Comment coverage on PR
      if: github.event_name == 'pull_request' && matrix.node-version == '20.x'
      uses: romeovs/lcov-reporter-action@v0.3.1
      with:
        lcov-file: ./apps/api/coverage/lcov.info
        github-token: ${{ secrets.GITHUB_TOKEN }}
        title: API Test Coverage
