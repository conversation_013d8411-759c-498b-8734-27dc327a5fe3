import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import type { Request, Response } from 'express';
import { logger } from './logger.js';

// Rate limiting configuration
export const rateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requests per minute per IP
  message: {
    error: 'Too many requests',
    message: 'Rate limit exceeded. Please try again later.',
    retryAfter: '60 seconds'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Custom key generator to handle proxied requests
  keyGenerator: (req: Request) => {
    // Use X-Forwarded-For header if available (for proxied requests)
    const forwarded = req.headers['x-forwarded-for'];
    if (forwarded && typeof forwarded === 'string') {
      return forwarded.split(',')[0].trim();
    }
    return req.ip;
  },
  // Custom handler for rate limit exceeded
  handler: (req: Request, res: Response) => {
    const clientIP = req.ip;
    const forwardedFor = req.headers['x-forwarded-for'];
    
    logger.warn('Rate limit exceeded', {
      clientIP,
      forwardedFor,
      endpoint: req.path,
      method: req.method,
      userAgent: req.headers['user-agent']
    });
    
    res.status(429).json({
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: '60 seconds'
    });
  },
  // Skip rate limiting for health checks
  skip: (req: Request) => {
    return req.path === '/health';
  }
});

// Helmet configuration for security headers
export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://openrouter.ai"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  // Cross Origin Embedder Policy
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  // Cross Origin Resource Policy
  crossOriginResourcePolicy: { policy: "cross-origin" },
  // DNS Prefetch Control
  dnsPrefetchControl: { allow: false },
  // Frame Options
  frameguard: { action: 'deny' },
  // Hide Powered By
  hidePoweredBy: true,
  // HSTS (HTTPS Strict Transport Security)
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  // IE No Open
  ieNoOpen: true,
  // No Sniff
  noSniff: true,
  // Origin Agent Cluster
  originAgentCluster: true,
  // Permitted Cross Domain Policies
  permittedCrossDomainPolicies: false,
  // Referrer Policy
  referrerPolicy: { policy: "no-referrer" },
  // X-XSS-Protection
  xssFilter: true
});

// HTTPS enforcement middleware (for production)
export function enforceHTTPS(req: Request, res: Response, next: () => void) {
  // Skip in development
  if (process.env.NODE_ENV !== 'production') {
    return next();
  }
  
  // Check if request is secure
  const isSecure = req.secure || 
                   req.headers['x-forwarded-proto'] === 'https' ||
                   req.headers['x-forwarded-ssl'] === 'on';
  
  if (!isSecure) {
    logger.warn('Insecure HTTP request blocked', {
      clientIP: req.ip,
      endpoint: req.path,
      method: req.method,
      headers: {
        'x-forwarded-proto': req.headers['x-forwarded-proto'],
        'x-forwarded-ssl': req.headers['x-forwarded-ssl']
      }
    });
    
    return res.status(426).json({
      error: 'HTTPS Required',
      message: 'This API requires HTTPS. Please use https:// instead of http://'
    });
  }
  
  next();
}
