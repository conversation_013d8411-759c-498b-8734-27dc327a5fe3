# Essay Marker Client

The `markerClient.ts` provides AI-powered essay marking functionality for Year 5 Bahasa Melayu essays following KSSR 2022 guidelines.

## Features

- **Comprehensive Marking**: Evaluates essays across 6 key criteria:
  - Ideas & Content (0-25 points)
  - Structure & Organization (0-20 points)
  - Language Use (0-20 points)
  - Vocabulary (0-15 points)
  - Mechanics: Spelling & Punctuation (0-10 points)
  - Creativity & Originality (0-10 points)

- **KSSR 2022 Compliance**: Follows official Malaysian curriculum guidelines
- **Achievement Bands**: Provides standardized achievement levels (Cemerlang, Kepujian, Baik, Memuaskan, TPM, TM)
- **Detailed Feedback**: Offers specific comments and actionable next steps

## Usage

### Basic Usage

```typescript
import { getEssayMarking, EssayMarkingResult } from '../utils/markerClient';

const essayContent = {
  introduction: "Saya suka bermain bola sepak...",
  mainPoints: [
    "<PERSON><PERSON><PERSON>, bola sepak adalah sukan yang menyeronokkan...",
    "<PERSON><PERSON>, ia membantu saya kekal sihat..."
  ],
  conclusion: "Kesimpulannya, bola sepak adalah hobi kegemaran saya..."
};

const result = await getEssayMarking("Hobi Kegemaran Saya", essayContent);

if (result) {
  console.log(`Total Score: ${result.total}/100`);
  console.log(`Converted Score: ${result.convertedScore}/20`);
  console.log(`Achievement Band: ${result.band}`);
}
```

### Response Structure

```typescript
interface EssayMarkingResult {
  ideasContent: { score: number; comment: string };
  structureOrganization: { score: number; comment: string };
  languageUse: { score: number; comment: string };
  vocabulary: { score: number; comment: string };
  mechanics: { score: number; comment: string };
  creativity: { score: number; comment: string };
  total: number;
  convertedScore: number;
  band: string;
  keyFeedback: string[];
  nextStepAction: string;
}
```

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Essay marking model: Uses Claude 3 Opus for detailed analytical marking
OPENROUTER_MODEL_MARKER=anthropic/claude-3-opus
```

### Model Selection

The marker client uses the `VITE_OPENROUTER_MODEL_MARKER` environment variable, falling back to `VITE_OPENROUTER_MODEL_QUESTIONS` if not specified.

## Integration

The marker client is integrated into the `ReviewScreen` component and automatically marks essays when students submit their work. The marking process:

1. **Automatic Triggering**: Marking starts when the ReviewScreen loads
2. **Loading State**: Shows a spinner while the AI processes the essay
3. **Error Handling**: Displays error messages and retry options if marking fails
4. **Results Display**: Shows comprehensive marking results with visual indicators

## Achievement Bands

| Band | Score Range | Description |
|------|-------------|-------------|
| Cemerlang (Excellent) | 18-20 | Task fully met; language accurate & varied; presentation exemplary |
| Kepujian (Merit) | 15-17 | Minor errors; well-developed ideas; good vocabulary & structure |
| Baik (Good) | 13-14 | Some errors but ideas clear; adequate vocabulary; mostly correct format |
| Memuaskan (Satisfactory) | 8-12 | Many errors; limited elaboration; inconsistent paragraphing |
| TPM (Minimum Mastery) | 3-7 | Ideas partly relevant; frequent grammar issues; poor organization |
| TM (Below Minimum) | 0-2 | Ideas irrelevant/missing; very poor language; no proper format |

## Error Handling

The marker client includes comprehensive error handling:

- **Network Errors**: Handles API connectivity issues
- **Parsing Errors**: Manages malformed AI responses
- **Missing Content**: Handles essays with missing sections
- **Rate Limiting**: Manages API rate limits gracefully

## Logging

All marker operations are logged using the debug logger with the 'Marker' category for easy debugging and monitoring.
