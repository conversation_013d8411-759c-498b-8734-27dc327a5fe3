import { OpenRouterClient, ChatRequest, ChatResponse } from '../clients/openRouter.js';

export interface ChatServiceRequest {
  messages: ChatRequest['messages'];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface ChatServiceResponse {
  success: boolean;
  data?: ChatResponse;
  error?: string;
  statusCode?: number;
}

export class ChatService {
  constructor(private openRouterClient: OpenRouterClient) {}

  async handleStudentPrompt(request: ChatServiceRequest): Promise<ChatServiceResponse> {
    try {
      // Validate request
      if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
        return {
          success: false,
          error: 'Invalid request: messages array is required and cannot be empty',
          statusCode: 400
        };
      }

      // Default model if not provided
      const selectedModel = request.model || 'anthropic/claude-3-haiku';

      // Call OpenRouter client
      const response = await this.openRouterClient.complete({
        messages: request.messages,
        model: selectedModel,
        temperature: request.temperature || 0.7,
        max_tokens: request.max_tokens || 150
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('Error in ChatService.handleStudentPrompt:', error);
      
      // Handle OpenRouter API errors specifically
      if (error instanceof Error && error.message.includes('OpenRouter API error')) {
        const statusMatch = error.message.match(/OpenRouter API error: (\d+)/);
        const statusCode = statusMatch ? parseInt(statusMatch[1]) : 500;
        
        return {
          success: false,
          error: 'Failed to get response from AI service',
          statusCode
        };
      }

      return {
        success: false,
        error: 'Internal server error',
        statusCode: 500
      };
    }
  }
}
