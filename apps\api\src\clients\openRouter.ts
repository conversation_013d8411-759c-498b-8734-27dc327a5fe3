export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatRequest {
  messages: ChatMessage[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

export interface OpenRouterClient {
  complete(request: ChatRequest): Promise<ChatResponse>;
}

export class OpenRouterClientImpl implements OpenRouterClient {
  constructor(private apiKey: string) {}

  async complete(request: ChatRequest): Promise<ChatResponse> {
    const { messages, model = 'anthropic/claude-3-haiku', temperature = 0.7, max_tokens = 150 } = request;

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'BM Essay Tutor'
      },
      body: JSON.stringify({
        messages,
        model,
        temperature,
        max_tokens
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
    }

    return await response.json();
  }
}
