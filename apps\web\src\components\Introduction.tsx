import React from 'react';
const Introduction = ({
  content,
  onChange
}) => {
  return <div className="border border-gray-300 rounded-lg overflow-hidden focus-within:border-blue-400 focus-within:ring-2 focus-within:ring-blue-100 transition duration-200">
      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
        <span className="text-sm text-gray-600">Pengenalan (Introduction)</span>
      </div>
      <textarea value={content} onChange={e => onChange(e.target.value)} className="w-full p-4 min-h-[120px] focus:outline-none resize-none" placeholder="<PERSON><PERSON> pengenalan essay anda di sini... (Write your essay introduction here...)" />
    </div>;
};
export default Introduction;