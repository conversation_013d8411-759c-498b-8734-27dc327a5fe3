# BM Essay Tutor

A Bahasa Melayu essay tutoring application with AI-powered assistance for Year 5 students following KSSR 2022 guidelines.

## Architecture

This is a monorepo with two main applications:

- **`apps/web`** - React frontend (Vite + TypeScript)
- **`apps/api`** - Express API server (TypeScript + ESM)

The API server acts as a proxy to OpenRouter, keeping the API key secure on the backend.

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm (install with `npm install -g pnpm`)
- OpenRouter API key from [https://openrouter.ai/](https://openrouter.ai/)

### Setup

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd bm-essay-tutor
   pnpm install
   ```

2. **Configure the API:**
   ```bash
   cd apps/api
   cp .env.sample .env
   # Edit .env and add your OpenRouter API key:
   # OPENROUTER_API_KEY=your_actual_api_key_here
   ```

3. **Configure the web app (optional):**
   ```bash
   cd apps/web
   cp .env.sample .env
   # Customize model settings if needed
   ```

### Development

**Start both servers:**
```bash
pnpm dev:all
```

**Or start them separately:**
```bash
# Terminal 1 - API server
pnpm dev:api

# Terminal 2 - Web app
pnpm dev
```

- **Web app:** http://localhost:5173
- **API server:** http://localhost:3001
- **Health check:** http://localhost:3001/health

### Available Scripts

- `pnpm dev` - Start web app only
- `pnpm dev:api` - Start API server only
- `pnpm dev:all` - Start both servers concurrently
- `pnpm build` - Build web app for production
- `pnpm build:api` - Build API server for production
- `pnpm lint` - Lint both applications

## API Endpoints

### `POST /api/chat`

Proxies chat requests to OpenRouter without exposing the API key.

**Request:**
```json
{
  "messages": [
    {"role": "system", "content": "You are a helpful assistant"},
    {"role": "user", "content": "Hello"}
  ],
  "model": "anthropic/claude-3-haiku",
  "temperature": 0.7,
  "max_tokens": 150
}
```

**Response:**
```json
{
  "choices": [
    {
      "message": {
        "content": "Hello! How can I help you today?"
      }
    }
  ]
}
```

## Environment Variables

### API Server (`apps/api/.env`)
- `OPENROUTER_API_KEY` - Your OpenRouter API key (required)
- `PORT` - Server port (default: 3001)
- `NODE_ENV` - Environment (development/production)

### Web App (`apps/web/.env`)
- `VITE_API_BASE_URL` - API server URL (default: http://localhost:3001)
- `VITE_OPENROUTER_MODEL_*` - Model configurations for different tasks

## Security

- ✅ API key is stored securely on the backend
- ✅ Frontend never sees the OpenRouter API key
- ✅ CORS configured for development and production
- ✅ Environment files are git-ignored

## Deployment

The API server exports a `buildServer()` factory function for easy testing and deployment:

```typescript
import { buildServer } from './src/index.js';

const app = buildServer();
// Mount app without starting server
```
