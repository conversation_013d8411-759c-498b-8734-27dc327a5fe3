import { describe, it, expect, vi, beforeEach } from 'vitest';
import request from 'supertest';
import { ChatService } from '../services/chatService.js';
import type { OpenRouterClient, ChatResponse } from '../clients/openRouter.js';

// Mock OpenRouter client for integration tests
const mockOpenRouterClient: OpenRouterClient = {
  complete: vi.fn()
};

describe('API Integration Tests', () => {
  let app: any;
  let mockChatService: ChatService;

  beforeEach(async () => {
    vi.clearAllMocks();
    mockChatService = new ChatService(mockOpenRouterClient);

    // Mock environment variable to avoid the error
    vi.stubEnv('OPENROUTER_API_KEY', 'test-key');

    // Import buildServer inside the test to avoid module-level execution
    const { buildServer } = await import('../index.js');
    app = buildServer(mockChatService);
  });

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
      expect(new Date(response.body.timestamp)).toBeInstanceOf(Date);
    });
  });

  describe('POST /api/chat', () => {
    it('should return successful chat response', async () => {
      // Arrange
      const mockResponse: ChatResponse = {
        choices: [{
          message: {
            content: 'Hello! I can help you with your Bahasa Melayu essay.'
          }
        }]
      };

      vi.mocked(mockOpenRouterClient.complete).mockResolvedValue(mockResponse);

      const requestBody = {
        messages: [
          { role: 'user', content: 'Help me with my essay' }
        ],
        model: 'anthropic/claude-3-haiku',
        temperature: 0.7,
        max_tokens: 150
      };

      // Act
      const response = await request(app)
        .post('/api/chat')
        .send(requestBody)
        .expect(200);

      // Assert
      expect(response.body).toEqual(mockResponse);
      expect(mockOpenRouterClient.complete).toHaveBeenCalledWith({
        messages: requestBody.messages,
        model: 'anthropic/claude-3-haiku',
        temperature: 0.7,
        max_tokens: 150
      });
    });

    it('should handle missing messages array', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid request data');
      expect(mockOpenRouterClient.complete).not.toHaveBeenCalled();
    });

    it('should handle empty messages array', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({ messages: [] })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid request data');
      expect(mockOpenRouterClient.complete).not.toHaveBeenCalled();
    });

    it('should handle OpenRouter API errors', async () => {
      // Arrange
      const apiError = new Error('OpenRouter API error: 429 Rate limit exceeded');
      vi.mocked(mockOpenRouterClient.complete).mockRejectedValue(apiError);

      const requestBody = {
        messages: [
          { role: 'user', content: 'Test message' }
        ]
      };

      // Act
      const response = await request(app)
        .post('/api/chat')
        .send(requestBody)
        .expect(429);

      // Assert
      expect(response.body).toHaveProperty('error', 'Failed to get response from AI service');
    });

    it('should handle generic server errors', async () => {
      // Arrange
      const genericError = new Error('Network error');
      vi.mocked(mockOpenRouterClient.complete).mockRejectedValue(genericError);

      const requestBody = {
        messages: [
          { role: 'user', content: 'Test message' }
        ]
      };

      // Act
      const response = await request(app)
        .post('/api/chat')
        .send(requestBody)
        .expect(500);

      // Assert
      expect(response.body).toHaveProperty('error', 'Internal server error');
    });

    it('should use default model when not specified', async () => {
      // Arrange
      const mockResponse: ChatResponse = {
        choices: [{
          message: {
            content: 'Response with default model'
          }
        }]
      };

      vi.mocked(mockOpenRouterClient.complete).mockResolvedValue(mockResponse);

      const requestBody = {
        messages: [
          { role: 'user', content: 'Test without model' }
        ]
      };

      // Act
      const response = await request(app)
        .post('/api/chat')
        .send(requestBody)
        .expect(200);

      // Assert
      expect(response.body).toEqual(mockResponse);
      expect(mockOpenRouterClient.complete).toHaveBeenCalledWith({
        messages: requestBody.messages,
        model: 'anthropic/claude-3-haiku',
        temperature: 0.7,
        max_tokens: 150
      });
    });

    it('should handle CORS headers', async () => {
      const response = await request(app)
        .get('/health')
        .set('Origin', 'http://localhost:5173')
        .expect(200);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });

    it('should handle unexpected errors in chat endpoint', async () => {
      // Arrange - Mock the service to throw an unexpected error
      vi.mocked(mockOpenRouterClient.complete).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      const requestBody = {
        messages: [
          { role: 'user', content: 'Test message' }
        ]
      };

      // Act
      const response = await request(app)
        .post('/api/chat')
        .send(requestBody)
        .expect(500);

      // Assert
      expect(response.body).toHaveProperty('error', 'Internal server error');
    });
  });

  describe('buildServer without service parameter', () => {
    it('should throw error when OPENROUTER_API_KEY is not set', async () => {
      // Arrange - Clear the environment variable
      delete process.env.OPENROUTER_API_KEY;

      // Act & Assert
      const { buildServer } = await import('../index.js');
      expect(() => buildServer()).toThrow('OPENROUTER_API_KEY not found in environment variables');
    });
  });
});
