import { vi } from 'vitest';
import type { OpenRouterClient, ChatResponse } from '../../clients/openRouter.js';

/**
 * Creates a mock OpenRouter client for testing
 */
export function createMockOpenRouterClient(): OpenRouterClient {
  return {
    complete: vi.fn()
  };
}

/**
 * Creates a standard successful chat response for testing
 */
export function createMockChatResponse(content: string = 'Test response'): ChatResponse {
  return {
    choices: [{
      message: {
        content
      }
    }]
  };
}

/**
 * Creates a mock error for OpenRouter API testing
 */
export function createOpenRouterError(statusCode: number, message: string = 'API Error'): Error {
  return new Error(`OpenRouter API error: ${statusCode} ${message}`);
}

/**
 * Standard test messages for consistent testing
 */
export const TEST_MESSAGES = {
  simple: [{ role: 'user' as const, content: 'Hello' }],
  essay: [
    { role: 'system' as const, content: 'You are a Bahasa Melayu essay tutor.' },
    { role: 'user' as const, content: 'Please help me improve my essay about friendship.' }
  ],
  empty: []
};

/**
 * Standard test configurations
 */
export const TEST_CONFIG = {
  defaultModel: 'anthropic/claude-3-haiku',
  defaultTemperature: 0.7,
  defaultMaxTokens: 150
};

/**
 * Helper to create a complete chat request for testing
 */
export function createChatRequest(overrides: any = {}) {
  return {
    messages: TEST_MESSAGES.simple,
    model: TEST_CONFIG.defaultModel,
    temperature: TEST_CONFIG.defaultTemperature,
    max_tokens: TEST_CONFIG.defaultMaxTokens,
    ...overrides
  };
}
