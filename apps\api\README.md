# BM Essay Tutor API

A Node.js/Express API for the Bahasa Melayu Essay Tutor application with comprehensive test coverage.

## 🚀 Quick Start

```bash
# Install dependencies
pnpm install

# Run in development mode
pnpm dev

# Run tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Build for production
pnpm build

# Start production server
pnpm start
```

## 🧪 Testing

This project has comprehensive test coverage using **Vitest** with the following test types:

### Test Commands

- `pnpm test` - Run all tests with coverage report
- `pnpm test:watch` - Run tests in watch mode for development
- `pnpm --filter api test` - Run tests from monorepo root

### Test Coverage

- **89.04% line coverage** (target: ≥85%)
- **83.33% branch coverage** (target: ≥80%)
- **100% coverage** in services and clients folders
- **22 tests total** across 3 test suites

### Test Structure

```
src/
├── __tests__/
│   └── integration.test.ts     # Express app integration tests
├── clients/
│   ├── __tests__/
│   │   └── openRouter.test.ts  # HTTP client unit tests
│   └── openRouter.ts
├── services/
│   ├── __tests__/
│   │   └── chatService.test.ts # Business logic unit tests
│   └── chatService.ts
└── index.ts                    # Express app setup
```

### Test Types

1. **Unit Tests** - Test individual functions/classes in isolation
   - Chat service business logic
   - OpenRouter client HTTP calls
   - All dependencies are mocked

2. **Integration Tests** - Test complete request/response cycles
   - Express routes with Supertest
   - End-to-end API behavior
   - Error handling and edge cases

## 🏗️ Architecture

The API follows a clean architecture pattern with dependency injection:

### Layers

1. **Routes** (`index.ts`) - Express route handlers
2. **Services** (`services/`) - Business logic layer
3. **Clients** (`clients/`) - External API integration layer

### Key Features

- **Dependency Injection** - Services accept interfaces, not concrete implementations
- **Error Handling** - Comprehensive error handling with proper HTTP status codes
- **Type Safety** - Full TypeScript coverage with strict types
- **Testability** - Pure functions and injectable dependencies

## 🔧 Configuration

### Environment Variables

```bash
OPENROUTER_API_KEY=your_api_key_here
NODE_ENV=development|production
LOG_LEVEL=info|debug|warn|error
```

### Vitest Configuration

Coverage thresholds are configured in `vitest.config.ts`:

- Lines: 85%
- Functions: 80%
- Branches: 80%
- Statements: 85%

## 🔒 Security Features

### Production Security
- **HTTPS Only** - TLS termination handled by Vercel/Cloudflare in production
- **CORS Allow-list** - Only `https://bm-essay-tutor.my` (prod) and `http://localhost:5173` (dev)
- **Rate Limiting** - 10 requests/minute per IP using `express-rate-limit`
- **Input Validation** - Zod schemas cap payload size (≤ 4 messages × 200 chars each)
- **Error Handling** - No API key or stack trace leakage in production
- **Structured Logging** - Pino logger with configurable levels
- **Security Headers** - Helmet.js for comprehensive security headers

### Request Validation
- Maximum 4 messages per request
- Maximum 200 characters per message
- Maximum 10KB request payload size
- Strict type validation with Zod schemas

### Logging
- Structured JSON logging in production
- Pretty-printed logs in development
- Automatic request ID generation
- Sensitive data redaction (API keys, auth headers)

## 📝 API Endpoints

### Health Check
```
GET /health
```
Returns server status and timestamp.

### Chat Completion
```
POST /api/chat
Content-Type: application/json

{
  "messages": [
    {"role": "user", "content": "Help me with my essay"}
  ],
  "model": "anthropic/claude-3-haiku",
  "temperature": 0.7,
  "max_tokens": 150
}
```

## 🛠️ Development

### Adding New Features

1. Write tests first (TDD approach)
2. Implement the feature
3. Ensure all tests pass
4. Check coverage meets thresholds

### Test Guidelines

- **Unit tests** should mock all external dependencies
- **Integration tests** should test the full request/response cycle
- Aim for ≥90% coverage on business logic (services)
- Test both happy path and error scenarios

## 🚨 Error Handling

The API provides consistent error responses:

```json
{
  "error": "Error message",
  "statusCode": 400
}
```

Common status codes:
- `400` - Bad Request (invalid input)
- `429` - Rate Limited (OpenRouter API)
- `500` - Internal Server Error
